{"root": ["./react-router.config.ts", "./vite.config.ts", "./app/protected-layout.tsx", "./app/root.tsx", "./app/routes.ts", "./app/api/api.ts", "./app/api/endpoint.ts", "./app/asset/icons/icon-assets.tsx", "./app/components/calendar/advance-calendar.tsx", "./app/components/calendar/calendar-header-left.tsx", "./app/components/calendar/calendar-order-item.tsx", "./app/components/calendar/day-view.tsx", "./app/components/calendar/month-view.tsx", "./app/components/calendar/order-block.tsx", "./app/components/calendar/order-list-drawer.tsx", "./app/components/calendar/order-payment.tsx", "./app/components/calendar/service-item.tsx", "./app/components/calendar/week-view.tsx", "./app/components/category/category-addon-table-header.tsx", "./app/components/category/category-search-interface.tsx", "./app/components/category/category-table-header.tsx", "./app/components/category/new-category-addon-card.tsx", "./app/components/category/task-information/task-information-dialog.tsx", "./app/components/category/task-information/task-information-item.tsx", "./app/components/category/task-information/task-information-panel.tsx", "./app/components/common/service-details-component.tsx", "./app/components/common/address-picker.tsx", "./app/components/common/app-sidebar.tsx", "./app/components/common/assign-cleaner.tsx", "./app/components/common/assign-customer-card.tsx", "./app/components/common/banner-column.tsx", "./app/components/common/banner-header.tsx", "./app/components/common/category-and-product-selection.tsx", "./app/components/common/cleaner-expertise.tsx", "./app/components/common/column-user-info.tsx", "./app/components/common/combobox.tsx", "./app/components/common/content-wrapper.tsx", "./app/components/common/custom-pagination.tsx", "./app/components/common/custom-select-api.tsx", "./app/components/common/custom-select.tsx", "./app/components/common/custom-tabs.tsx", "./app/components/common/customer-dialog.tsx", "./app/components/common/daily-toggle.tsx", "./app/components/common/date-picker.tsx", "./app/components/common/date-range-picker-v2.tsx", "./app/components/common/date-range-picker.tsx", "./app/components/common/date-time-picker.tsx", "./app/components/common/drag-drop-file-upload.tsx", "./app/components/common/expendable-form-input.tsx", "./app/components/common/form-input-multiple-languages.tsx", "./app/components/common/form-input.tsx", "./app/components/common/form-textarea-popover.tsx", "./app/components/common/header-right.tsx", "./app/components/common/input-multiple-langs-dialog.tsx", "./app/components/common/multiple-select-search.tsx", "./app/components/common/notification-drawer.tsx", "./app/components/common/order-add-cleaner.tsx", "./app/components/common/order-address.tsx", "./app/components/common/order-assign-cleaner.tsx", "./app/components/common/order-cleaner.tsx", "./app/components/common/order-complete-service-card.tsx", "./app/components/common/order-component.tsx", "./app/components/common/order-date.tsx", "./app/components/common/order-deatil-cancel-reason.tsx", "./app/components/common/order-detail-card.tsx", "./app/components/common/order-detail-header-old.tsx", "./app/components/common/order-detail-header.tsx", "./app/components/common/order-edit-assign-cleaner.tsx", "./app/components/common/order-in-progress-service.tsx", "./app/components/common/order-input-readonly.tsx", "./app/components/common/order-payment-label.tsx", "./app/components/common/order-payment.tsx", "./app/components/common/order-profile.tsx", "./app/components/common/order-service-icon.tsx", "./app/components/common/overview-card.tsx", "./app/components/common/password-input.tsx", "./app/components/common/payment-dialog.tsx", "./app/components/common/pop-up-ui.tsx", "./app/components/common/profile-picker.tsx", "./app/components/common/scheduling.tsx", "./app/components/common/search-bar.tsx", "./app/components/common/service-detail-popup-form.tsx", "./app/components/common/service-details-content.tsx", "./app/components/common/service-details-dialog.tsx", "./app/components/common/service-details-v2.tsx", "./app/components/common/service-input-box-selection-data.tsx", "./app/components/common/service-service-type-selection-data.tsx", "./app/components/common/service-service-type-selection.tsx", "./app/components/common/single-date-picker.tsx", "./app/components/common/star-rating.tsx", "./app/components/common/table-pagination.tsx", "./app/components/common/target-users-form.tsx", "./app/components/common/upload-attachment.tsx", "./app/components/common/uploader.tsx", "./app/components/common/viewmore-popover.tsx", "./app/components/common/customer-service/last-contact-date.tsx", "./app/components/common/customer-service/preferred-service.tsx", "./app/components/common/customer-service/remark.tsx", "./app/components/common/dashboard/dashboard-kpi.tsx", "./app/components/common/direct-sale/direct-sale-customer-form.tsx", "./app/components/common/direct-sale/direct-sale-detail-content.tsx", "./app/components/common/direct-sale/direct-sale-dialog.tsx", "./app/components/common/direct-sale/direct-sale-discount.tsx", "./app/components/common/direct-sale/direct-sale-payment.tsx", "./app/components/common/direct-sale/service-option.tsx", "./app/components/common/draggable/deselectable-radio-button.tsx", "./app/components/common/draggable/draggable-combobox-panel.tsx", "./app/components/common/draggable/draggable-context.tsx", "./app/components/common/draggable/draggable-input-panel.tsx", "./app/components/common/draggable/draggable-inputbox-panel.tsx", "./app/components/common/draggable/sortable-combo-box.tsx", "./app/components/common/draggable/sortable-input.tsx", "./app/components/common/draggable/sortable-task-item.tsx", "./app/components/common/draggable/time-picker.tsx", "./app/components/common/drawer/customer-review-drawer.tsx", "./app/components/common/drawer/last-contact-date-drawer.tsx", "./app/components/common/overview/overview-age.tsx", "./app/components/common/overview/overview-chart.tsx", "./app/components/common/overview/overview-gender.tsx", "./app/components/common/overview/overview-kpi.tsx", "./app/components/common/overview/overview-prefer-service.tsx", "./app/components/common/overview/overview-referral-source.tsx", "./app/components/common/overview/overview-service-breakdown.tsx", "./app/components/data-table/b-combo-header.tsx", "./app/components/data-table/bcombo-columns.tsx", "./app/components/data-table/block-schedule-header.tsx", "./app/components/data-table/blocked-schedule-columns.tsx", "./app/components/data-table/category-addon-columns.tsx", "./app/components/data-table/category-columns.tsx", "./app/components/data-table/cleaner-columns.tsx", "./app/components/data-table/cleaner-header.tsx", "./app/components/data-table/coupon-columns.tsx", "./app/components/data-table/coupon-header.tsx", "./app/components/data-table/customer-column.tsx", "./app/components/data-table/customer-header.tsx", "./app/components/data-table/data-table-header.tsx", "./app/components/data-table/direct-sale-columns.tsx", "./app/components/data-table/direct-sale-customer-column.tsx", "./app/components/data-table/direct-sale-customer-header.tsx", "./app/components/data-table/direct-sale-header.tsx", "./app/components/data-table/finance-columns.tsx", "./app/components/data-table/finance-header.tsx", "./app/components/data-table/item-columns.tsx", "./app/components/data-table/item-header.tsx", "./app/components/data-table/otp-columns.tsx", "./app/components/data-table/payment-link-columns.tsx", "./app/components/data-table/payment-link-header.tsx", "./app/components/data-table/product-columns.tsx", "./app/components/data-table/product-header.tsx", "./app/components/data-table/product-option-columns.tsx", "./app/components/data-table/product-option-header.tsx", "./app/components/data-table/promotion-columns.tsx", "./app/components/data-table/promotion-header.tsx", "./app/components/data-table/push-notification-column.tsx", "./app/components/data-table/push-notification-header.tsx", "./app/components/data-table/referral-columns.tsx", "./app/components/data-table/referral-header.tsx", "./app/components/data-table/referral-table-header.tsx", "./app/components/data-table/registered-customer-column.tsx", "./app/components/data-table/registered-customer-header.tsx", "./app/components/data-table/role-column.tsx", "./app/components/data-table/role-header.tsx", "./app/components/data-table/service-bundle-columns.tsx", "./app/components/data-table/service-bundle-header.tsx", "./app/components/data-table/sortable-rows.tsx", "./app/components/data-table/table-cell-div.tsx", "./app/components/data-table/table-row-skeleton.tsx", "./app/components/data-table/table-rows.tsx", "./app/components/data-table/top-up-header.tsx", "./app/components/data-table/topup-columns.tsx", "./app/components/data-table/user-columns.tsx", "./app/components/data-table/user-header.tsx", "./app/components/data-table/voucher-columns.tsx", "./app/components/data-table/voucher-header.tsx", "./app/components/headers/app-header.tsx", "./app/components/headers/custom-header.tsx", "./app/components/headers/header-title.tsx", "./app/components/order/form-input-with-qty.tsx", "./app/components/order/index.tsx", "./app/components/order/order-header-left.tsx", "./app/components/order/order-header.tsx", "./app/components/signin/signin-form.tsx", "./app/components/ui/alert-dialog.tsx", "./app/components/ui/avatar.tsx", "./app/components/ui/badge.tsx", "./app/components/ui/button.tsx", "./app/components/ui/calendar.tsx", "./app/components/ui/card.tsx", "./app/components/ui/chart.tsx", "./app/components/ui/checkbox.tsx", "./app/components/ui/collapsible.tsx", "./app/components/ui/command.tsx", "./app/components/ui/dialog.tsx", "./app/components/ui/drawer.tsx", "./app/components/ui/dropdown-menu.tsx", "./app/components/ui/form.tsx", "./app/components/ui/input.tsx", "./app/components/ui/ios-toggle.tsx", "./app/components/ui/label.tsx", "./app/components/ui/pagination.tsx", "./app/components/ui/popover.tsx", "./app/components/ui/radio-group.tsx", "./app/components/ui/select.tsx", "./app/components/ui/separator.tsx", "./app/components/ui/sheet.tsx", "./app/components/ui/sidebar.tsx", "./app/components/ui/skeleton.tsx", "./app/components/ui/sonner.tsx", "./app/components/ui/switch.tsx", "./app/components/ui/table.tsx", "./app/components/ui/tabs.tsx", "./app/components/ui/textarea.tsx", "./app/components/ui/tooltip.tsx", "./app/constants/constants.ts", "./app/constants/data-dummy.ts", "./app/constants/query-key-enum.ts", "./app/hooks/use-add-blocked-schedule-mutation.ts", "./app/hooks/use-add-cleaner-detail-mutation.ts", "./app/hooks/use-add-cleaner-mutation.ts", "./app/hooks/use-add-payment-link-mutation.ts", "./app/hooks/use-add-service-item-details-mutation.ts", "./app/hooks/use-add-service-item-mutation.ts", "./app/hooks/use-add-topup-mutation.ts", "./app/hooks/use-add-user-to-coupon-mutation.ts", "./app/hooks/use-announcement-mutation.ts", "./app/hooks/use-announcement-query.ts", "./app/hooks/use-auth.ts", "./app/hooks/use-block-shcedule-list-query.ts", "./app/hooks/use-blocked-schedule-detail-query.ts", "./app/hooks/use-calendar-order-query.ts", "./app/hooks/use-can-go-back.ts", "./app/hooks/use-cancel-order-mutation.ts", "./app/hooks/use-category-form.ts", "./app/hooks/use-category-icon-direct-sale-query.ts", "./app/hooks/use-category-name-query.ts", "./app/hooks/use-category-query.ts", "./app/hooks/use-category-rearrange-mutation.ts", "./app/hooks/use-check-aba-payment-status-query.ts", "./app/hooks/use-check-payment-status-mutation.ts", "./app/hooks/use-check-topup-payment-status-mutation.ts", "./app/hooks/use-cleaner-detail-query.ts", "./app/hooks/use-cleaners-query.ts", "./app/hooks/use-coupon-detail-query.ts", "./app/hooks/use-coupon-query-summary.ts", "./app/hooks/use-coupon-query.ts", "./app/hooks/use-create-coupon-mutation.ts", "./app/hooks/use-create-direct-sale-customer-mutation.ts", "./app/hooks/use-create-direct-sale-mutation.ts", "./app/hooks/use-custom-query-client.ts", "./app/hooks/use-customer-direct-sale-user-query.ts", "./app/hooks/use-customer-query.ts", "./app/hooks/use-customer-rating-query.ts", "./app/hooks/use-data-table-api.ts", "./app/hooks/use-data-table-config.ts", "./app/hooks/use-delete-receipt-mutation.ts", "./app/hooks/use-direct-sale-effect.ts", "./app/hooks/use-direct-sale-preview-mutation.ts", "./app/hooks/use-direct-sale-user-query.ts", "./app/hooks/use-edit-order-note-mutation.ts", "./app/hooks/use-existed-customer-query.ts", "./app/hooks/use-export-excel-mutation.ts", "./app/hooks/use-get-data-direct-sale-query.ts", "./app/hooks/use-last-contact-date-query.ts", "./app/hooks/use-list-direct-sale-query.ts", "./app/hooks/use-marketing-query.ts", "./app/hooks/use-mobile.ts", "./app/hooks/use-order-detail-query.ts", "./app/hooks/use-order-query.ts", "./app/hooks/use-otp-query.ts", "./app/hooks/use-payment-link-query.ts", "./app/hooks/use-permission.ts", "./app/hooks/use-print-invoice-mutation.ts", "./app/hooks/use-print-mutation.ts", "./app/hooks/use-products-product-option-query.ts", "./app/hooks/use-profile-mutation.ts", "./app/hooks/use-registered-customer-query.ts", "./app/hooks/use-remove-user-from-coupon-mutation.ts", "./app/hooks/use-resouce-referral-query.ts", "./app/hooks/use-schedule-mutation.ts", "./app/hooks/use-selected-product-detail-query.ts", "./app/hooks/use-service-item-detail-query.ts", "./app/hooks/use-service-item-query.ts", "./app/hooks/use-table-state.ts", "./app/hooks/use-topup-query.ts", "./app/hooks/use-update-cleaner-expertises-mutation.ts", "./app/hooks/use-update-customer-service-mutation.ts", "./app/hooks/use-update-last-contact-mutation.ts", "./app/hooks/use-update-order-status-mutation.tsx", "./app/hooks/use-update-payment-status-mutation.ts", "./app/hooks/usepopupdialog.ts", "./app/lib/chart-helper.ts", "./app/lib/date-helper.ts", "./app/lib/distance-helper.ts", "./app/lib/permission.ts", "./app/lib/socket.ts", "./app/lib/utils.ts", "./app/lib/schema/banner-schema.ts", "./app/lib/schema/block-schedule-schema.ts", "./app/lib/schema/category-addon-schema.ts", "./app/lib/schema/category-schema.ts", "./app/lib/schema/cleaner-schema.ts", "./app/lib/schema/coupon-schema.ts", "./app/lib/schema/direct-sale-schema.ts", "./app/lib/schema/multi-lang-schema.ts", "./app/lib/schema/order-schema.ts", "./app/lib/schema/payment-link-schema.ts", "./app/lib/schema/product-option-schema.ts", "./app/lib/schema/product-schema.ts", "./app/lib/schema/profile-schema.ts", "./app/lib/schema/promotion-schema.ts", "./app/lib/schema/push-notification-schema copy.ts", "./app/lib/schema/push-notification-schema.ts", "./app/lib/schema/role-schema.ts", "./app/lib/schema/service-bundle-schema.ts", "./app/lib/schema/service-item-schema.ts", "./app/lib/schema/signin-schema.ts", "./app/lib/schema/topup-schema.ts", "./app/lib/schema/user-schema.ts", "./app/lib/schema/voucher-schema.ts", "./app/locales/locales.ts", "./app/routes/banner.tsx", "./app/routes/client-online-route.tsx", "./app/routes/dashboard-layout.tsx", "./app/routes/dashboard.tsx", "./app/routes/new-banner.tsx", "./app/routes/new-promotion.tsx", "./app/routes/new-push-notification copy.tsx", "./app/routes/new-push-notification.tsx", "./app/routes/new-role.tsx", "./app/routes/new-user.tsx", "./app/routes/otp.tsx", "./app/routes/overview.tsx", "./app/routes/push-notification.tsx", "./app/routes/referral-program.tsx", "./app/routes/roles.tsx", "./app/routes/users.tsx", "./app/routes/auth/forgot-password.tsx", "./app/routes/auth/login.tsx", "./app/routes/blocked-schedule/blocked-schedule.tsx", "./app/routes/blocked-schedule/new-blocked-schedule.tsx", "./app/routes/calendar/calendar.tsx", "./app/routes/calendar/layout.tsx", "./app/routes/category/category.tsx", "./app/routes/category/new-category.tsx", "./app/routes/category-addon/category-addon.tsx", "./app/routes/category-addon/new-category-addon.tsx", "./app/routes/cleaner/cleaner.tsx", "./app/routes/cleaner/new-cleaner.tsx", "./app/routes/coupon/coupon.tsx", "./app/routes/coupon/new-coupon.tsx", "./app/routes/customer-services/customer.tsx", "./app/routes/customer-services/direct-sale-customer.tsx", "./app/routes/customer-services/registered-customer.tsx", "./app/routes/finance/b-combos.tsx", "./app/routes/finance/direct-sales.tsx", "./app/routes/finance/finance-orders.tsx", "./app/routes/finance/new-top-up.tsx", "./app/routes/finance/top-up.tsx", "./app/routes/item/item.tsx", "./app/routes/item/new-item.tsx", "./app/routes/order/layout.tsx", "./app/routes/order/order-old.tsx", "./app/routes/order/order.tsx", "./app/routes/payment-link/new-payment-link.tsx", "./app/routes/payment-link/payment-link.tsx", "./app/routes/product/new-product.tsx", "./app/routes/product/product.tsx", "./app/routes/product-option/new-product-option.tsx", "./app/routes/product-option/product-option.tsx", "./app/routes/promotion/new-promotion.tsx", "./app/routes/promotion/promotions.tsx", "./app/routes/service-bundle/new-service-bundle.tsx", "./app/routes/service-bundle/service-bundle.tsx", "./app/routes/voucher/edit-voucher.tsx", "./app/routes/voucher/new-voucher.tsx", "./app/routes/voucher/voucher.tsx", "./app/store/auth-store.ts", "./app/store/calendar-store.ts", "./app/types/api.d.ts", "./app/types/auth.d.ts", "./app/types/constants.d.ts", "./app/types/tanstack-table.d.ts", "./app/welcome/welcome.tsx", "./.react-router/types/+future.ts", "./.react-router/types/+routes.ts", "./.react-router/types/+server-build.d.ts", "./.react-router/types/app/+types/protected-layout.ts", "./.react-router/types/app/+types/root.ts", "./.react-router/types/app/routes/+types/customer.ts", "./.react-router/types/app/routes/+types/dashboard-layout.ts", "./.react-router/types/app/routes/+types/dashboard.ts", "./.react-router/types/app/routes/+types/new-push-notification.ts", "./.react-router/types/app/routes/+types/otp.ts", "./.react-router/types/app/routes/+types/overview.ts", "./.react-router/types/app/routes/+types/push-notification.ts", "./.react-router/types/app/routes/auth/+types/login.ts", "./.react-router/types/app/routes/blocked-schedule/+types/blocked-schedule.ts", "./.react-router/types/app/routes/blocked-schedule/+types/new-blocked-schedule.ts", "./.react-router/types/app/routes/calendar/+types/calendar.ts", "./.react-router/types/app/routes/calendar/+types/layout.ts", "./.react-router/types/app/routes/category/+types/category.ts", "./.react-router/types/app/routes/category/+types/new-category.ts", "./.react-router/types/app/routes/category-addon/+types/category-addon.ts", "./.react-router/types/app/routes/category-addon/+types/new-category-addon.ts", "./.react-router/types/app/routes/cleaner/+types/cleaner.ts", "./.react-router/types/app/routes/cleaner/+types/new-cleaner.ts", "./.react-router/types/app/routes/coupon/+types/coupon.ts", "./.react-router/types/app/routes/coupon/+types/new-coupon.ts", "./.react-router/types/app/routes/finance/+types/b-combos.ts", "./.react-router/types/app/routes/finance/+types/direct-sales.ts", "./.react-router/types/app/routes/finance/+types/finance-orders.ts", "./.react-router/types/app/routes/finance/+types/new-top-up.ts", "./.react-router/types/app/routes/finance/+types/top-up.ts", "./.react-router/types/app/routes/item/+types/item.ts", "./.react-router/types/app/routes/item/+types/new-item.ts", "./.react-router/types/app/routes/order/+types/layout.ts", "./.react-router/types/app/routes/order/+types/order.ts", "./.react-router/types/app/routes/payment-link/+types/new-payment-link.ts", "./.react-router/types/app/routes/payment-link/+types/payment-link.ts", "./.react-router/types/app/routes/product/+types/new-product.ts", "./.react-router/types/app/routes/product/+types/product.ts", "./.react-router/types/app/routes/product-option/+types/new-product-option.ts", "./.react-router/types/app/routes/product-option/+types/product-option.ts"], "errors": true, "version": "5.8.3"}