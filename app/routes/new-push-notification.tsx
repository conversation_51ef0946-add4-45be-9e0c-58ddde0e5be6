import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import CustomHeader from '@/components/headers/custom-header';
import { Form, FormField } from '@/components/ui/form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm, type Control, type Path } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import FormInputMultipleLanguages from '@/components/common/form-input-multiple-languages';
import {
  pushNotificationSchema,
  type PushNotificationSchemaProps
} from '@/lib/schema/push-notification-schema';
import FormInput from '@/components/common/form-input';
import { Uploader } from '@/components/common/uploader';
import TargetUsersForm from '@/components/common/target-users-form';
import { useAnnouncementMutation } from '@/hooks/use-announcement-mutation';

export default function NewPushNotification() {
  const { t } = useTranslation();
  const { mutate, isPending } = useAnnouncementMutation();
  const form = useForm<PushNotificationSchemaProps>({
    mode: 'onSubmit',
    resolver: zodResolver(pushNotificationSchema),
    defaultValues: {
      title: { en: '', km: '', vi: '', tw: '', cn: '' },
      description: {
        en: '',
        km: '',
        vi: '',
        tw: '',
        cn: ''
      },
      name: '',
      bannerCn: undefined,
      bannerEn: undefined,
      bannerKm: undefined,
      bannerTw: undefined,
      bannerVi: undefined,
      topics: [
        {
          id: '1',
          userType: 'user_type_all',
          gender: undefined,
          age: undefined
        }
      ]
    }
  });

  const onSubmit = async (values: PushNotificationSchemaProps) => {
    const topicsConverted = values.topics.map((item) => {
      return [item.age, item.gender, item.userType] // keep all values
        .filter(Boolean) // remove undefined / falsy
        .join(','); // join with comma
    });

    const preparePayload = {
      name: values.name,
      bannerCn: values.bannerCn,
      bannerEn: values.bannerEn,
      bannerKm: values.bannerKm,
      bannerTw: values.bannerTw,
      bannerVi: values.bannerVi,
      titleEn: values.title.en,
      titleKm: values.title.km,
      titleVi: values.title.vi,
      titleTw: values.title.tw,
      titleCn: values.title.cn,
      contentEn: values.description.en,
      contentKm: values.description.km,
      contentVi: values.description.vi,
      contentTw: values.description.tw,
      contentCn: values.description.cn,
      topics: topicsConverted
    };
    mutate(preparePayload);
  };

  return (
    <div className="h-screen flex flex-col">
      {/* i want this customer header to be fixed not scrollable */}
      <div className="shrink-0 sticky top-0 z-10">
        <CustomHeader
          isLoading={isPending}
          buttonText="send"
          onSave={form.handleSubmit(onSubmit, (error) => console.log({ error }))}
        />
      </div>

      <div className="bg-muted flex-1 oversslow-y-auto">
        <Form {...form}>
          <form
            // onSubmit={form.handleSubmit(onSubmit, (error) => console.log({ error }))}
            className="p-6 gap-6 flex flex-col min-h-full"
          >
            {/* i want this to be scroll */}
            <div className="flex flex-col gap-4">
              <Card className="w-full">
                <CardHeader className="gap-4">
                  <CardTitle>{t('productPage.details')}</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex w-full gap-4">
                    <FormInputMultipleLanguages
                      form={form}
                      name="title"
                      label="Title"
                      placeholder="Title"
                    />
                    <FormInput placeholder="Name" label="Name" control={form.control} name="name" />
                  </div>
                  <FormInputMultipleLanguages
                    form={form}
                    name="description"
                    label="Description"
                    placeholder="Description"
                    isTextArea
                  />
                  <div className="grid grid-cols-5 gap-4">
                    <ImageUpload label="Banner En" control={form.control} name="bannerEn" />
                    <ImageUpload label="Banner Km" control={form.control} name="bannerKm" />
                    <ImageUpload label="Banner Vi" control={form.control} name="bannerVi" />
                    <ImageUpload label="Banner Cn" control={form.control} name="bannerCn" />
                    <ImageUpload label="Banner Tw" control={form.control} name="bannerTw" />
                  </div>
                </CardContent>
              </Card>

              <TargetUsersForm control={form.control} />
            </div>
          </form>
        </Form>
      </div>
    </div>
  );
}

type Props = {
  label: string;
  control: Control<PushNotificationSchemaProps>;
  name: Path<PushNotificationSchemaProps>;
};

const ImageUpload = ({ label, control, name }: Props) => {
  return (
    <div className="aspect-square">
      <FormField
        control={control}
        name={name}
        render={({ field }) => (
          <div className="space-y-2">
            <div>{label}</div>
            <div className="rounded-lg overflow-hidden shadow-sm aspect-square">
              {field.value ? (
                <img
                  src={URL.createObjectURL(field.value as never)}
                  alt={label}
                  className="w-full h-full object-cover"
                />
              ) : (
                <Uploader
                  className="aspect-square"
                  multiple={false}
                  onUploaded={(files) => field.onChange(files[0])}
                />
              )}
            </div>
          </div>
        )}
      />
    </div>
  );
};
