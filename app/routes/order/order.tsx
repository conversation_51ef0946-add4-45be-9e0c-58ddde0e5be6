import OrderComponent from '@/components/common/order-component';
import { toast } from 'sonner';
import { useEffect, useRef, useState } from 'react';
import OrderDetailCard from '@/components/common/order-detail-card';
import CategorySearchInterface from '@/components/category/category-search-interface';
import useOrderQuery from '@/hooks/use-order-query';
import useOrderDetailQuery from '@/hooks/use-order-detail-query';
import { Skeleton } from '@/components/ui/skeleton';
import OrderHeaderDetail from '@/components/common/order-detail-header';
import OrderHeaderLeft from '@/components/order/order-header-left';
import ServiceDetailsV2 from '@/components/common/service-details-v2';
import PaymentInfo from '@/components/common/order-payment';

import { useUpdateOrderStatusMutation } from '@/hooks/use-update-order-status-mutation';
import socket from '@/lib/socket';
import { ServiceDetailsDialog } from '@/components/common/service-details-dialog';
import { RefreshCcw } from 'lucide-react';
import useCategoryIconDirectSaleQuery from '@/hooks/use-category-icon-direct-sale-query';
import { get } from 'lodash';
import { Card } from '@/components/ui/card';
import { Edit01Icon } from 'hugeicons-react';
import { Textarea } from '@/components/ui/textarea';
import useEdtiOrderNoteMutation from '@/hooks/use-edit-order-note-mutation';
import { ACTIONS, MODULES } from '@/lib/permission';
import useAuthStore from '@/store/auth-store';
import { useSearchParams } from 'react-router';
import UploadAttachment from '@/components/common/upload-attachment';
import CustomPagination from '@/components/common/custom-pagination';

export const handle = {
  module: MODULES.ORDER,
  action: ACTIONS.VIEW
};

export default function Order() {
  const { user } = useAuthStore();
  const defaultType = user?.type == 'SALES' ? 'DIRECT_SALE' : '';
  // console.log('defaultType:', defaultType);
  const [searchParams] = useSearchParams();
  const bulkOrderIdQuery = searchParams.get('bulkOrderId');

  const [statusFilter, setStatusFilter] = useState('all');
  const isCalledRef = useRef(false);
  const [bulkIdSelected, setBulkIdSelected] = useState<string>('');

  const [currentPage, setCurrentPage] = useState(0);
  const [searchValue, setSearchValue] = useState('');
  const [type, setType] = useState<OrderTypeProps>(defaultType || 'all');
  const [paymentStatusFilter, setPaymentStatusFilter] = useState('all');
  const [dateRange, setDateRange] = useState<{ from?: Date; to?: Date }>({});
  const {
    data: ordersData,
    isPending,
    refetch: refetchOrders
  } = useOrderQuery({
    currentPage: currentPage,
    statusFilter,
    dateRange,
    searchText: searchValue,
    paymentStatusFilter,
    type
  });
  const [permission, setPermission] = useState(
    !('Notification' in window) ? undefined : Notification.permission
  );
  const { data: orderDetailData, refetch, isFetching } = useOrderDetailQuery(bulkIdSelected);
  const { mutateAsync, isPending: isUpdatePending } = useUpdateOrderStatusMutation();

  const [isCalendarOpen, setIsCalendarOpen] = useState(false);
  const ordersArray: OrderListAttributes[] = ordersData?.data || [];

  useEffect(() => {
    try {
      if (!('Notification' in window)) {
        console.log('This browser does not support desktop notification');
        return;
      }
      if (Notification.permission !== 'granted') {
        requestPermission();
      }
    } catch (error) {
      console.log({ error });
    }
  }, []);

  const requestPermission = async () => {
    const result = await Notification.requestPermission();
    setPermission(result);
    return result;
  };

  useEffect(() => {
    const showNotification = (title: string, body: string) => {
      toast.success(body);
      refetchOrders();
      if (permission === 'granted') {
        console.log('call tas');
        const notification = new Notification(title, {
          body,
          icon: '/beasy-icon.png'
        });

        notification.onclick = () => {
          window.focus();
          notification.close();
        };
      } else if (permission === 'default') {
        requestPermission().then((newPermission) => {
          if (newPermission === 'granted') {
            showNotification(title, body);
          }
        });
      } else {
        alert('Notification permission was denied. Please enable it in your browser settings.');
      }
    };

    if (isCalledRef.current) return;
    socket.on('connect', () => {
      console.log('Connected to server');
    });
    socket.on('updateData', ({ payload }: { payload: { title?: string; body?: string } }) => {
      showNotification(payload?.title || '', payload?.body || '');
    });
    return () => {
      isCalledRef.current = true;
    };
  }, [permission, refetchOrders]);

  useEffect(() => {
    setBulkIdSelected('');
  }, [statusFilter, dateRange, searchValue, paymentStatusFilter, currentPage]);

  useEffect(() => {
    if (bulkOrderIdQuery) setBulkIdSelected(bulkOrderIdQuery);
  }, [bulkOrderIdQuery]);

  const handleOrderClick = async () => {
    if (!orderDetailData) return;
    if (orderDetailData.status === 'PENDING') {
      await mutateAsync({ bulkOrderId: orderDetailData.bulkOrderId, status: 'ACCEPTED' });
    } else if (orderDetailData.status === 'ACCEPTED') {
      await mutateAsync({ bulkOrderId: orderDetailData.bulkOrderId, status: 'COMPLETED' });
    }
    refetch();
    refetchOrders();
  };

  if (isPending)
    return (
      <div className="flex items-center h-full">
        <div className="h-full w-[400px] gap-10 flex flex-col items-center" />
        <Skeleton className="h-full flex-1" />
      </div>
    );

  return (
    <div className="flex flex-rows h-screen overflow-hidden">
      <div className="w-[390px]">
        <OrderHeaderLeft />
        <div className="flex flex-col h-[calc(100vh-88px)]">
          <CategorySearchInterface
            statusFilter={statusFilter}
            setStatusFilter={setStatusFilter}
            dateRange={dateRange}
            setDateRange={setDateRange}
            isCalendarOpen={isCalendarOpen}
            setIsCalendarOpen={setIsCalendarOpen}
            searchValue={searchValue}
            setSearchValue={setSearchValue}
            paymentStatusFilter={paymentStatusFilter}
            setPaymentStatusFilter={setPaymentStatusFilter}
            type={type}
            setType={!defaultType ? (val) => setType(val as OrderTypeProps) : undefined}
          />
          <div className="flex flex-col h-full overflow-y-scroll px-4 gap-2">
            {ordersArray.map((order, index: number) => (
              <div
                key={index}
                className="cursor-pointer"
                onClick={() => {
                  setBulkIdSelected(order.bulkOrderId);
                }}
              >
                <OrderComponent isActive={bulkIdSelected === order.bulkOrderId} order={order} />
              </div>
            ))}
          </div>
          <div className="w-full">
            <CustomPagination
              currentPage={currentPage || 0}
              totalPages={ordersData?.pagination?.totalPages || 0}
              onPageChange={(val) => {
                console.log({ val });
                setCurrentPage(val);
              }}
            />
          </div>
        </div>
      </div>
      <div className="flex flex-1 flex-col overflow-hidden items-start bg-muted">
        {isFetching ? (
          <div className="flex-1 justify-center items-center flex w-full gap-4">
            <span className="text-sm text-muted-foreground">Loading Please Wait ... </span>
            <RefreshCcw className="h-5 w-5 transition-transform cursor-pointer animate-spin text-primary" />
          </div>
        ) : orderDetailData ? (
          <>
            <OrderHeaderDetail
              orderDetail={orderDetailData}
              status={orderDetailData.status || 'PENDING'}
              type={orderDetailData.type}
              orderId={orderDetailData.bulkOrderId}
              isPending={isUpdatePending}
              paymentMethodDisplay={orderDetailData.paymentMethodDisplay || ''}
              paymentStatus={orderDetailData.paymentStatus}
              onClick={handleOrderClick}
            />
            <OrderDetailContent orderDetailData={orderDetailData} />
          </>
        ) : (
          <div className="flex-1 justify-center items-center flex w-full">
            <span className="text-sm text-muted-foreground">
              Please Select Order to View Detail
            </span>
          </div>
        )}
      </div>
    </div>
  );
}

const OrderDetailContent = ({ orderDetailData }: { orderDetailData: OrderListAttributes }) => {
  const { data } = useCategoryIconDirectSaleQuery();
  const { mutateAsync } = useEdtiOrderNoteMutation();
  const [note, setNote] = useState<string>(orderDetailData.note || '');
  const [isEdit, setIsEdit] = useState(false);

  const handleSave = async () => {
    try {
      await mutateAsync({ id: orderDetailData.bulkOrderId, note });
      setIsEdit(false);
    } catch (error) {
      console.log({ error });
      toast.error('Failed to update note');
    }
    // call api here
  };

  return (
    <div className="w-full overflow-hidden h-[calc(100vh-88px)]">
      <div className="overflow-y-scroll h-full p-4">
        <OrderDetailCard data={orderDetailData} />
        <div className="flex gap-4 flex-row pt-4">
          <div className="flex flex-1 flex-col gap-4">
            {orderDetailData?.items?.map((service, index) => (
              <ServiceDetailsV2
                icon={get(data, [service.categoryNameEn], service.thumbnailUrl)}
                index={index}
                key={index}
                service={service}
              />
            ))}
            <ServiceDetailsDialog
              bulkOrderId={orderDetailData.bulkOrderId}
              serviceItemDetails={orderDetailData.serviceItemDetails}
            />
          </div>
          <div className="w-[340px]">
            <Card className="w-[340px] mx-auto bg-card p-6 gap-2 mb-4">
              <div className="flex justify-between">
                <h2 className="text-base font-bold text-gray-700">Remark</h2>
                {isEdit ? (
                  <div className="cursor-pointer text-primary text-sm" onClick={handleSave}>
                    Save
                  </div>
                ) : (
                  <Edit01Icon
                    onClick={() => setIsEdit(true)}
                    className="cursor-pointer text-primary"
                  />
                )}
              </div>
              <Textarea disabled={!isEdit} value={note} onChange={(e) => setNote(e.target.value)} />
            </Card>

            {orderDetailData.type === 'DIRECT_SALE' && (
              <Card className="w-[340px] mx-auto bg-card p-6 gap-6 mb-4">
                {orderDetailData.sale && (
                  <div>
                    <div className="flex justify-between">
                      <h2 className="text-base font-bold text-gray-700">Sales Agent</h2>
                    </div>
                    <div>{`${orderDetailData.sale?.firstName} ${orderDetailData.sale?.lastName} (${orderDetailData.sale?.username})`}</div>
                  </div>
                )}
                {orderDetailData.reseller && (
                  <div>
                    <div className="flex justify-between">
                      <h2 className="text-base font-bold text-gray-700">Reseller</h2>
                    </div>
                    <div>{`${orderDetailData.reseller?.firstName} ${orderDetailData.reseller?.lastName} (${orderDetailData.reseller?.username})`}</div>
                  </div>
                )}
              </Card>
            )}
            {orderDetailData.type === 'DIRECT_SALE' && (
              <UploadAttachment orderDetailData={orderDetailData} />
            )}
            <PaymentInfo
              paymentStatus={orderDetailData.paymentStatus}
              paymentMethod={orderDetailData.paymentMethod}
              tranId={orderDetailData.tranId}
              orderCleaners={orderDetailData.cleaners || []}
              bulkOrderId={orderDetailData.bulkOrderId}
              couponCode={orderDetailData?.couponCode || ''}
              serviceFeeDisplay={orderDetailData?.serviceFeeDisplay || ''}
              discountDisplay={orderDetailData?.discountDisplay || ''}
              transportFeeDisplay={orderDetailData?.transportFeeDisplay || ''}
              totalAmountDisplay={orderDetailData?.totalAmountDisplay || ''}
              subTotalDisplay={orderDetailData?.subTotalDisplay || ''}
              vatFeeDisplay={orderDetailData?.vatFeeDisplay || ''}
              paymentMethodDisplay={orderDetailData?.paymentMethodDisplay || ''}
              totalPayableAmountDisplay={orderDetailData?.totalPayableAmountDisplay || ''}
              type={orderDetailData?.type || 'ORDER'}
              depositDisplay={orderDetailData.depositDisplay}
              remainingDisplay={orderDetailData.remainingDisplay}
              directSaleNextPaymentDate={orderDetailData.directSaleNextPaymentDate}
            />
          </div>
        </div>
      </div>
    </div>
  );
};
