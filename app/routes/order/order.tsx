import OrderComponent from '@/components/common/order-component';
import { toast } from 'sonner';
import { useEffect, useRef, useState } from 'react';
import OrderDetailCard from '@/components/common/order-detail-card';
import CategorySearchInterface from '@/components/category/category-search-interface';
import useOrderQuery from '@/hooks/use-order-query';
import useOrderDetailQuery from '@/hooks/use-order-detail-query';
import { Skeleton } from '@/components/ui/skeleton';
import OrderHeaderDetail from '@/components/common/order-detail-header';
import OrderHeaderLeft from '@/components/order/order-header-left';
import { CustomPagination } from '@/components/common/Custom-Pagination';
import ServiceDetailsV2 from '@/components/common/service-details-v2';
import PaymentInfo from '@/components/common/order-payment';

import { useUpdateOrderStatusMutation } from '@/hooks/use-update-order-status-mutation';
import socket from '@/lib/socket';
import { ServiceDetailsDialog } from '@/components/common/service-details-dialog';

export default function Order() {
  const [statusFilter, setStatusFilter] = useState('all');
  const isCalledRef = useRef(false);
  const [bulkIdSelected, setBulkIdSelected] = useState<string>('');
  const [currentPage, setCurrentPage] = useState(0);
  const [searchValue, setSearchValue] = useState('');
  const [paymentStatusFilter, setPaymentStatusFilter] = useState('Paid');
  const [dateRange, setDateRange] = useState<{ from?: Date; to?: Date }>({});
  const {
    data: ordersData,
    isPending,
    refetch: refetchOrders
  } = useOrderQuery({
    currentPage: currentPage,
    statusFilter,
    dateRange,
    searchText: searchValue,
    paymentStatusFilter
  });
  const [permission, setPermission] = useState(Notification.permission);
  const { data: orderDetailData, refetch } = useOrderDetailQuery(bulkIdSelected);
  const { mutateAsync, isPending: isUpdatePending } = useUpdateOrderStatusMutation();

  const [isCalendarOpen, setIsCalendarOpen] = useState(false);
  const ordersArray: OrderListAttributes[] = ordersData?.data || [];

  useEffect(() => {
    if (!('Notification' in window)) {
      console.log('This browser does not support desktop notification');
      return;
    }
    if (Notification.permission !== 'granted') {
      requestPermission();
    }
  }, []);

  const requestPermission = async () => {
    const result = await Notification.requestPermission();
    setPermission(result);
    return result;
  };

  useEffect(() => {
    const showNotification = (title: string, body: string) => {
      toast.success(body);
      refetchOrders();
      if (permission === 'granted') {
        console.log('call tas');
        const notification = new Notification(title, {
          body,
          icon: '/beasy-icon.png'
        });

        notification.onclick = () => {
          window.focus();
          notification.close();
        };
      } else if (permission === 'default') {
        requestPermission().then((newPermission) => {
          if (newPermission === 'granted') {
            showNotification(title, body);
          }
        });
      } else {
        alert('Notification permission was denied. Please enable it in your browser settings.');
      }
    };

    if (isCalledRef.current) return;
    socket.on('connect', () => {
      console.log('Connected to server');
    });
    socket.on('updateData', ({ payload }: { payload: { title?: string; body?: string } }) => {
      showNotification(payload?.title || '', payload?.body || '');
    });
    return () => {
      isCalledRef.current = true;
    };
  }, [permission, refetchOrders]);

  useEffect(() => {
    setBulkIdSelected('');
  }, [statusFilter, dateRange, searchValue, paymentStatusFilter, currentPage]);

  const handleOrderClick = async () => {
    if (!orderDetailData) return;
    if (orderDetailData.status === 'PENDING') {
      await mutateAsync({ bulkOrderId: orderDetailData.bulkOrderId, status: 'ACCEPTED' });
    } else if (orderDetailData.status === 'ACCEPTED') {
      await mutateAsync({ bulkOrderId: orderDetailData.bulkOrderId, status: 'COMPLETED' });
    }
    refetch();
    refetchOrders();
  };

  if (isPending)
    return (
      <div className="flex items-center h-full">
        <div className="h-full w-[400px] gap-10 flex flex-col items-center" />
        <Skeleton className="h-full flex-1" />
      </div>
    );

  return (
    <div className="flex flex-rows h-screen overflow-hidden">
      <div className="w-[390px]">
        <OrderHeaderLeft />
        <div className="flex flex-col h-[calc(100vh-88px)]">
          <CategorySearchInterface
            statusFilter={statusFilter}
            setStatusFilter={setStatusFilter}
            dateRange={dateRange}
            setDateRange={setDateRange}
            isCalendarOpen={isCalendarOpen}
            setIsCalendarOpen={setIsCalendarOpen}
            searchValue={searchValue}
            setSearchValue={setSearchValue}
            paymentStatusFilter={paymentStatusFilter}
            setPaymentStatusFilter={setPaymentStatusFilter}
          />
          <div className="flex flex-col h-full overflow-scroll px-4 gap-2">
            {ordersArray.map((order, index: number) => (
              <div
                key={index}
                className="cursor-pointer"
                onClick={() => {
                  setBulkIdSelected(order.bulkOrderId);
                }}
              >
                <OrderComponent isActive={bulkIdSelected === order.bulkOrderId} order={order} />
              </div>
            ))}
          </div>
          <div className="w-full">
            <CustomPagination
              currentPage={currentPage || 0}
              totalPages={ordersData?.pagination?.totalPages || 0}
              onPageChange={(val) => {
                console.log({ val });
                setCurrentPage(val);
              }}
            />
          </div>
        </div>
      </div>
      <div className="flex flex-1 flex-col overflow-hidden items-start bg-muted">
        {orderDetailData ? (
          <>
            <OrderHeaderDetail
              status={orderDetailData.status || 'PENDING'}
              orderId={orderDetailData.bulkOrderId}
              isPending={isUpdatePending}
              paymentMethodDisplay={orderDetailData.paymentMethodDisplay}
              paymentStatus={orderDetailData.paymentStatus}
              onClick={handleOrderClick}
            />
            <OrderDetailContent orderDetailData={orderDetailData} />
          </>
        ) : (
          <div className="flex-1 justify-center items-center flex w-full">
            <span className="text-sm text-muted-foreground">
              Please Select Order to View Detail
            </span>
          </div>
        )}
      </div>
    </div>
  );
}

const OrderDetailContent = ({ orderDetailData }: { orderDetailData: OrderListAttributes }) => {
  return (
    <div className="w-full overflow-hidden h-[calc(100vh-88px)]">
      <div className="overflow-scroll h-full p-4">
        <OrderDetailCard data={orderDetailData} />
        <div className="flex gap-4 flex-row  pt-4">
          <div className="flex flex-1 flex-col gap-4">
            {orderDetailData?.items?.map((service, index) => (
              <ServiceDetailsV2 index={index} key={index} service={service} />
            ))}
            <ServiceDetailsDialog
              bulkOrderId={orderDetailData.bulkOrderId}
              serviceItemDetails={orderDetailData.serviceItemDetails}
            />
          </div>
          <div>
            <PaymentInfo
              orderCleaners={orderDetailData.cleaners || []}
              bulkOrderId={orderDetailData.bulkOrderId}
              couponCode={orderDetailData?.couponCode || ''}
              serviceFeeDisplay={orderDetailData?.serviceFeeDisplay || ''}
              discountDisplay={orderDetailData?.discountDisplay || ''}
              transportFeeDisplay={orderDetailData?.transportFeeDisplay || ''}
              totalAmountDisplay={orderDetailData?.totalAmountDisplay || ''}
              subTotalDisplay={orderDetailData?.subTotalDisplay || ''}
              vatFeeDisplay={orderDetailData?.vatFeeDisplay || ''}
              paymentMethodDisplay={orderDetailData?.paymentMethodDisplay || ''}
              totalPayableAmountDisplay={orderDetailData?.totalPayableAmountDisplay || ''}
            />
          </div>
        </div>
      </div>
    </div>
  );
};
