import OverviewAge from '@/components/common/overview/overview-age';
import OverviewChart from '@/components/common/overview/overview-chart';
import OverviewGender from '@/components/common/overview/overview-gender';
import OverviewKpi from '@/components/common/overview/overview-kpi';
import OverviewPreferService from '@/components/common/overview/overview-prefer-service';
import OverviewReferralSource from '@/components/common/overview/overview-referral-source';
import OverviewServiceBreakdown from '@/components/common/overview/overview-service-breakdown';
import { useState } from 'react';
import { useMarketingOverviewQuery } from '@/hooks/use-marketing-query';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';

export default function Overview() {
  const [dateType, setDateType] = useState<string>('LAST_7_DAYS');
  const { data, isPending } = useMarketingOverviewQuery(dateType);

  return (
    <div className="p-5 pt-0 flex gap-6 flex-col">
      <div className="sticky top-0 z-10 flex flex-wrap items-center justify-end gap-4 py-2 bg-white">
        <Select value={dateType} onValueChange={setDateType}>
          <SelectTrigger className="w-[180px]">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {/* <SelectItem value="TODAY">Today</SelectItem>
            <SelectItem value="YESTERDAY">Yesterday</SelectItem> */}
            <SelectItem value="LAST_7_DAYS">Last 7 Days</SelectItem>
            <SelectItem value="THIS_MONTH">This Month</SelectItem>
            <SelectItem value="LAST_MONTH">Last Month</SelectItem>
            <SelectItem value="LAST_90_DAYS">Last 90 days</SelectItem>
          </SelectContent>
        </Select>
        {/* <Button size="sm" onClick={() => {}}>
          Export
          <IconAssets.Export />
        </Button> */}
      </div>
      <OverviewKpi
        isLoading={isPending}
        userCount={data?.totalUserRegistered}
        activeUserCount={data?.totalActiveUserCount}
        inActiveUserCount={data?.totalInactive}
        orderCount={data?.totalOrderCount}
        notInterestedCount={data?.totalNotInterested}
        interestedButNotNowCount={data?.totalInterestedButNotNow}
        noAnswerCount={data?.totalNoAnswer}
        existingCustomerCount={data?.totalExistingCustomer}
      />
      <OverviewChart data={data?.overviewChart} dateType={dateType} isLoading={isPending} />
      <div className="flex gap-6 h-[240px]">
        <OverviewGender data={data?.genderResult || []} isLoading={isPending} />
        <OverviewAge data={data?.ageGroup || []} isLoading={isPending} />
      </div>
      <div className="grid grid-cols-3 space-x-6">
        <OverviewReferralSource data={data?.referralSource || []} />
        <OverviewPreferService data={data?.categoriesPreferred || []} />
        <OverviewServiceBreakdown data={data?.servicesBreakdown || []} />
      </div>
    </div>
  );
}
