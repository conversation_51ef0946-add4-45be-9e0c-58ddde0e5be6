import * as React from 'react';
import { Table, TableBody } from '@/components/ui/table';
import useTableState from '@/hooks/use-table-state';
import TablePagination from '@/components/common/table-pagination';
import useDataTableConfig from '@/hooks/use-data-table-config';
import TableHeader from '@/components/data-table/data-table-header';
import TableRows from '@/components/data-table/table-rows';
import CleanerHeader from '@/components/data-table/cleaner-header';
import useCleanersQuery from '@/hooks/use-cleaners-query';
import { cleanerColumns } from '@/components/data-table/cleaner-columns';
import { ACTIONS, MODULES } from '@/lib/permission';

export const handle = {
  module: MODULES.CLEANER,
  action: ACTIONS.VIEW
};

export default function Cleaner() {
  const tableState = useTableState();
  const { data, isPending } = useCleanersQuery();

  const table = useDataTableConfig(data || [], cleanerColumns, tableState);

  return (
    <div className="flex flex-col h-[calc(100vh-88px)] overflow-hidden p-4 pb-0">
      <div className="rounded-md border flex flex-col flex-1 min-h-0">
        <CleanerHeader table={table} />
        <div className="flex min-h-0 overflow-hidden">
          <Table className="min-w-full">
            <TableHeader table={table} />
            <TableBody>
              <TableRows isLoading={isPending} columns={cleanerColumns} table={table} />
            </TableBody>
          </Table>
        </div>
      </div>
      <TablePagination table={table} />
    </div>
  );
}
