import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import CustomHeader from '@/components/headers/custom-header';
import { Form, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import CustomSelect from '@/components/common/custom-select';
import ContentWrapper from '@/components/common/content-wrapper';
import { useTranslation } from 'react-i18next';
import FormInput from '@/components/common/form-input';
import ProfilePicker from '@/components/common/profile-picker';
import { cleanerSchema, type CleanerSchemaProps } from '@/lib/schema/cleaner-schema';
import { useCreateCleanerMutation } from '@/hooks/use-add-cleaner-mutation';
import { useParams } from 'react-router';
import useCleanerDetailQuery from '@/hooks/use-cleaner-detail-query';
import { useEffect } from 'react';
import DatePicker from '@/components/common/date-picker';

export default function NewCleaner() {
  const { id } = useParams();
  const { data } = useCleanerDetailQuery(id);
  const { mutate, isPending } = useCreateCleanerMutation(id);
  const { t } = useTranslation();
  const form = useForm<CleanerSchemaProps>({
    mode: 'onSubmit',
    resolver: zodResolver(cleanerSchema),
    defaultValues: {
      cleanerName: '',
      status: 'Active',
      gender: 'MALE',
      joinedDate: new Date(),
      image: {
        file: undefined,
        url: ''
      }
    }
  });

  useEffect(() => {
    if (data) {
      form.reset({
        cleanerName: data.name,
        status: data.status ? 'Active' : 'Inactive',
        gender: data?.gender as 'MALE' | 'FEMALE',
        joinedDate: data?.joinedDate ? new Date(data?.joinedDate) : new Date(),
        image: {
          file: undefined,
          url: data.image || ''
        }
      });
    }
  }, [data, form]);

  const onSubmit = (values: CleanerSchemaProps) => {
    // console.log('User form submitted:', values);
    mutate(values);
  };

  return (
    <div>
      {/* Header with Save Button */}
      <CustomHeader
        isLoading={isPending}
        onSave={form.handleSubmit(onSubmit, (error) => console.log(error))}
      />

      <ContentWrapper>
        <div className="p-6 flex flex-row items-baseline">
          <Card className="flex flex-1 gap-4">
            <CardHeader>
              <CardTitle className="text-base">Account Information</CardTitle>
            </CardHeader>

            <CardContent className="px-6 pb-6">
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit, (error) => console.log(error))}>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-x-6 gap-y-6">
                    {/* Profile Picker */}
                    <div className="col-span-3">
                      <FormField
                        control={form.control}
                        name="image"
                        render={({ field }) => (
                          <FormItem className="flex flex-col">
                            <ProfilePicker
                              image={field.value && field.value.url}
                              setImage={field.onChange}
                            />
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormInput
                      control={form.control}
                      name="cleanerName"
                      label="Name (Khmer)"
                      placeholder="Name"
                    />

                    <FormField
                      control={form.control}
                      name="status"
                      render={({ field }) => (
                        <FormItem className="flex flex-col">
                          <FormLabel>{t('Status')}</FormLabel>
                          <CustomSelect
                            className="!h-10"
                            placeholder={t('Select status')}
                            data={[
                              { label: 'Active', value: 'Active' },
                              { label: 'Inactive', value: 'Inactive' }
                            ]}
                            value={field.value}
                            onValueChange={field.onChange}
                          />
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="gender"
                      render={({ field }) => (
                        <FormItem className="flex flex-col">
                          <FormLabel>Gender</FormLabel>
                          <CustomSelect
                            className="!h-10"
                            placeholder="Select Gender"
                            data={[
                              { label: 'Male', value: 'MALE' },
                              { label: 'Female', value: 'FEMALE' }
                            ]}
                            value={field.value}
                            onValueChange={field.onChange}
                          />
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="joinedDate"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Joined Date</FormLabel>
                          <DatePicker date={field.value} onDateTimeChange={field.onChange} />
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </form>
              </Form>
            </CardContent>
          </Card>
        </div>
      </ContentWrapper>
    </div>
  );
}
