import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import CustomHeader from '@/components/headers/custom-header';
import { Form, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import ContentWrapper from '@/components/common/content-wrapper';
import FormInput from '@/components/common/form-input';
import { useParams } from 'react-router';
import { couponSchema, type CouponSchemaProps } from '@/lib/schema/coupon-schema';
import CategoryAndProductSelection from '@/components/common/category-and-product-selection';
import FormInputMultipleLanguages from '@/components/common/form-input-multiple-languages';
import AssignCustomersCard from '@/components/common/assign-customer-card';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import CustomSelect from '@/components/common/custom-select';
import DatePicker from '@/components/common/date-picker';
import { useCreateCouponMutation } from '@/hooks/use-create-coupon-mutation';
import useCouponDetailQuery from '@/hooks/use-coupon-detail-query';
import { useEffect } from 'react';

export default function NewCleaner() {
  const { id } = useParams<{ id: string }>();
  const { data } = useCouponDetailQuery(id);

  const { mutate, isPending } = useCreateCouponMutation(id);
  const form = useForm<CouponSchemaProps>({
    mode: 'onSubmit',
    resolver: zodResolver(couponSchema),
    defaultValues: {
      users: [],
      selectedProducts: [],
      name: 'testing',
      code: 'testing',
      promoText: {
        en: 'testing',
        km: 'testing',
        vi: 'testing',
        tw: 'testing',
        cn: 'testing'
      },
      remark: '',
      value: '10',
      type: 'FIXED',
      targetUser: 'ALL',
      effectiveDate: undefined,
      expiredDate: undefined,
      maxRedeemAmount: undefined,
      maxRedeemPerPax: undefined,
      maxRedeemTotal: undefined,
      isNewUserOnly: false
    }
  });

  console.log({ data });
  useEffect(() => {
    if (data) {
      form.reset({ ...data });
    }
  }, [data, form]);

  const onSubmit = (values: CouponSchemaProps) => {
    console.log('User form submitted:', values);
    mutate({
      ...values,
      targetUser: (values.users || []).length > 0 ? 'SELECTED' : 'ALL',
      value: (values.type === 'PERCENTAGE'
        ? parseFloat(values.value) / 100
        : values.value
      ).toString(),
      promoTextEn: values.promoText.en,
      promoTextKm: values.promoText.km,
      promoTextVi: values.promoText.vi,
      promoTextTw: values.promoText.tw,
      promoTextCn: values.promoText.cn
    });
  };

  return (
    <div>
      {/* Header with Save Button */}
      <CustomHeader
        isLoading={isPending}
        onSave={form.handleSubmit(onSubmit, (error) => console.log(error))}
      />
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit, (error) => console.log(error))}>
          <ContentWrapper className="p-6 flex items-baseline gap-6">
            <div className="flex flex-col w-full gap-6">
              <Card className="space-y-4">
                <CardHeader>
                  <CardTitle className="text-base">Account Information</CardTitle>
                </CardHeader>

                <CardContent className="px-0 pb-6">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-x-6 gap-y-6 px-6">
                    <FormInput control={form.control} name="name" label="Name" placeholder="Name" />
                    <FormInput control={form.control} name="code" label="Code" placeholder="Code" />
                    <FormInputMultipleLanguages
                      form={form}
                      name="promoText"
                      label="Promotion Text"
                      placeholder="Promotion Text"
                    />
                    <FormInput
                      control={form.control}
                      name="value"
                      label="Value"
                      placeholder="Value"
                    />
                    <FormField
                      control={form.control}
                      name="type"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Type</FormLabel>
                          <CustomSelect
                            data={[
                              { label: 'Fixed', value: 'FIXED' },
                              { label: 'Percentage', value: 'PERCENTAGE' }
                            ]}
                            placeholder="Select Type"
                            value={field.value}
                            onValueChange={field.onChange}
                          />
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="effectiveDate"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Effective Date (Optional)</FormLabel>
                          <DatePicker date={field.value} onDateTimeChange={field.onChange} />
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="expiredDate"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Expired Date (Optional)</FormLabel>
                          <DatePicker date={field.value} onDateTimeChange={field.onChange} />
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormInput
                      control={form.control}
                      name="maxRedeemAmount"
                      label="Max Redeem Amount (Optional)"
                      placeholder="Max Redeem Amount"
                    />
                    <FormInput
                      control={form.control}
                      name="maxRedeemTotal"
                      label="Max Redeem Total (Optional)"
                      placeholder="Max Redeem Total"
                    />
                    <div className="flex col-span-3 gap-4 items-center">
                      <Checkbox id="newUser" />
                      <Label htmlFor="newUser">New User Only</Label>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <AssignCustomersCard control={form.control} name="users" />
            </div>
            <Card>
              <CardContent className="w-[400px]">
                <FormField
                  control={form.control}
                  name="selectedProducts"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="font-bold">Apply To</FormLabel>
                      <CategoryAndProductSelection
                        productIds={field.value}
                        onProductIdsChange={field.onChange}
                      />
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>
          </ContentWrapper>
        </form>
      </Form>
    </div>
  );
}
