import * as React from 'react';
import { Table, TableBody } from '@/components/ui/table';
import useTableState from '@/hooks/use-table-state';
import TablePagination from '@/components/common/table-pagination';
import useDataTableConfig from '@/hooks/use-data-table-config';
import TableHeader from '@/components/data-table/data-table-header';
import TableRows from '@/components/data-table/table-rows';
import useCouponQuery from '@/hooks/use-coupon-query';
import CouponHeader from '@/components/data-table/coupon-header';
import { couponColumns } from '@/components/data-table/coupon-columns';
import { ACTIONS, MODULES } from '@/lib/permission';

export const handle = {
  module: MODULES.MARKETING_COUPON,
  action: ACTIONS.VIEW
};

export default function Coupon() {
  const tableState = useTableState();
  const { data, isPending } = useCouponQuery({
    columnFilters: tableState.columnFilters
  });

  const table = useDataTableConfig(data || [], couponColumns, tableState);

  return (
    <div className="flex flex-col h-[calc(100vh-88px)] overflow-hidden p-4 pb-0">
      <div className="rounded-md border flex flex-col flex-1 min-h-0">
        <CouponHeader table={table} />
        <div className="flex min-h-0 overflow-hidden">
          <Table className="min-w-full">
            <TableHeader table={table} />
            <TableBody>
              <TableRows isLoading={isPending} columns={couponColumns} table={table} />
            </TableBody>
          </Table>
        </div>
      </div>
      <TablePagination table={table} />
    </div>
  );
}
