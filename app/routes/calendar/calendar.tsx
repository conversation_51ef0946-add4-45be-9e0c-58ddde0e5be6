import AdvanceCalendar, { type ViewType } from '@/components/calendar/advance-calendar';
import CalendarHeaderLeft from '@/components/calendar/calendar-header-left';
import CalendarOrderItem from '@/components/calendar/calendar-order-item';
import OrderListDrawer from '@/components/calendar/order-list-drawer';
import { Calendar as CalendarComponent } from '@/components/ui/calendar';
import useCalendarOrderQuery from '@/hooks/use-calendar-order-query';
import { useCalendarStore } from '@/store/calendar-store';
import { useEffect, useState } from 'react';
import { startOfWeek, endOfWeek, isEqual, startOfMonth, endOfMonth } from 'date-fns';
import type { DateRange } from 'react-day-picker';
import { ACTIONS, MODULES } from '@/lib/permission';

export const handle = {
  module: MODULES.ORDER,
  action: ACTIONS.VIEW
};

export default function Calendar() {
  const [isOpen, setIsOpen] = useState(false);
  const [viewType, setViewType] = useState<ViewType>('day');
  const [dateRange, setDateRange] = useState<{ from: Date; to: Date }>({
    from: new Date(),
    to: new Date()
  });
  const [date, setDate] = useState<Date>(new Date());
  const { setOrders } = useCalendarStore();

  const { data: ordersData, isPending } = useCalendarOrderQuery({
    statusFilter: ['ACCEPTED', 'COMPLETED'],
    dateRange: { from: dateRange.from, to: dateRange.to },
    searchText: '',
    paymentStatusFilter: '',
    type: 'all'
  });

  const orders: OrderListAttributes[] = ordersData?.data || [];

  const handleSelect = (dates: DateRange) => {
    // console.log('handleSelect dates: ', dates);
    let newDate;
    if (dates.from && dates.to) {
      if (!isEqual(dateRange.from, dates.from)) {
        newDate = dates.from;
      }
      if (!isEqual(dateRange.to, dates.to)) {
        newDate = dates.to;
      }
    }

    if (newDate) {
      handleSelectCalendar(newDate);
    }
  };

  const handleSelectCalendar = (newDate: Date) => {
    switch (viewType) {
      case 'day':
        if (newDate) setDateRange({ from: newDate, to: newDate });
        break;

      case 'week':
        if (newDate) setDateRange({ from: startOfWeek(newDate), to: endOfWeek(newDate) });
        break;

      case 'month':
        if (newDate) setDateRange({ from: startOfMonth(newDate), to: endOfMonth(newDate) });
        break;

      default:
        break;
    }
  };

  useEffect(() => {
    handleSelectCalendar(dateRange.from);
  }, [viewType]);

  useEffect(() => {
    if (ordersData) {
      // console.log('useEffect orders: ', orders);
      setOrders(ordersData.data);
    }
  }, [ordersData]);

  return (
    <div className="flex flex-rows h-screen overflow-hidden">
      <div className="w-[280px] border-r flex flex-col h-full">
        <div className="flex flex-col">
          <CalendarHeaderLeft />
          <CalendarComponent
            mode="range"
            required
            selected={dateRange}
            onSelect={handleSelect}
            // disabled={viewType === 'month'}
            // onMonthChange={handleMonthChange}
          />
          <div className="px-4 border-t pt-4 font-bold">Upcoming Order</div>
        </div>
        <div className="flex-1 overflow-auto px-4 pt-4 flex flex-col gap-4">
          {isPending ? (
            <div>loading..</div>
          ) : (
            orders.map((order, index) => <CalendarOrderItem key={index} order={order} />)
          )}
        </div>
      </div>
      <div className="flex h-full w-full flex-col items-start">
        <div className="flex-1 w-full flex flex-col items-center overflow-auto">
          <AdvanceCalendar
            {...{
              date: dateRange.from,
              setDate,
              dateRange,
              viewType,
              setViewType,
              handleEventClick: (date) => {
                if (date) {
                  setDate(date);
                  setIsOpen(true);
                }
              },
              handleSelectCalendar,
              handleTodayClick: () => {
                const newDate = new Date();
                handleSelectCalendar(newDate);
              }
            }}
          />
          <OrderListDrawer {...{ orders, date, isOpen, setIsOpen }} />
        </div>
      </div>
    </div>
  );
}
