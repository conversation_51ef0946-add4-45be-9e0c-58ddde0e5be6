import * as React from 'react';
import { Table } from '@/components/ui/table';
import useTableState from '@/hooks/use-table-state';
import { bannerData } from '@/constants/data-dummy';
import TablePagination from '@/components/common/table-pagination';
import useDataTableConfig from '@/hooks/use-data-table-config';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import SortableRows from '@/components/data-table/sortable-rows';

import BannerHeader from '@/components/common/banner-header';
import { bannerColumns } from '@/components/common/banner-column';
import DraggableContext from '@/components/common/draggable/draggable-context';
import type { UniqueIdentifier } from '@dnd-kit/core';
import DataTableHeader from '@/components/data-table/data-table-header';

function moveItem<T>(array: T[], from: number, to: number): T[] {
  const updated = [...array];
  const [removed] = updated.splice(from, 1);
  updated.splice(to, 0, removed);
  return updated;
}

export default function Product() {
  const tableState = useTableState();
  const [data, setData] = React.useState<BannerProps[]>(bannerData);
  const table = useDataTableConfig(data, bannerColumns, tableState);

  const {
    statusFilter,
    dateRange,
    isCalendarOpen,
    setStatusFilter,
    setDateRange,
    setIsCalendarOpen
  } = tableState;

  // Status filter with debounce
  React.useEffect(() => {
    const timeoutId = setTimeout(() => {
      const statusColumn = table.getColumn('status');
      if (statusColumn) {
        statusColumn.setFilterValue(statusFilter === 'all' ? undefined : statusFilter);
      }
    }, 100);
    return () => clearTimeout(timeoutId);
  }, [statusFilter, table]);

  const headerProps = {
    table,
    statusFilter,
    setStatusFilter,
    dateRange,
    setDateRange,
    isCalendarOpen,
    setIsCalendarOpen
  };

  const handleDragChange = (id: UniqueIdentifier, newIndex: number) => {
    const oldIndex = data.findIndex((item) => item.id === id);
    if (oldIndex === -1 || oldIndex === newIndex) return;
    setData((prev) => moveItem(prev, oldIndex, newIndex));
  };

  return (
    <div className="flex flex-col h-[calc(100vh-88px)] overflow-hidden p-4 pb-0">
      <div className="rounded-md border flex flex-col flex-1 min-h-0">
        <BannerHeader {...headerProps} />

        <DraggableContext data={data} setData={setData} onChange={handleDragChange}>
          <div className="flex min-h-0 overflow-hidden">
            <Table className="min-w-full">
              <DataTableHeader isDraggable table={table} />

              <SortableContext
                items={data.map((item) => item.id)}
                strategy={verticalListSortingStrategy}
              >
                <SortableRows table={table} columns={bannerColumns} />
              </SortableContext>
            </Table>
          </div>
        </DraggableContext>
      </div>

      <TablePagination table={table} />
    </div>
  );
}
