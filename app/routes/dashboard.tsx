import React from 'react';

const Dashboard: React.FC = () => {
  return <div className="flex flex-1 flex-col"></div>;
};

export default Dashboard;

// import { useState } from 'react';
// import {
//   Select,
//   SelectContent,
//   SelectItem,
//   SelectTrigger,
//   SelectValue
// } from '@/components/ui/select';
// import { Button } from '@/components/ui/button';
// import IconAssets from '@/asset/icons/icon-assets';
// import DashboardKpi from '@/components/common/dashboard/dashboard-kpi';
// import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';

// export default function Dashboard() {
//   const [dateType, setDateType] = useState<string>('LAST_7_DAYS');

//   return (
//     <div className="p-5 pt-0 flex gap-6 flex-col">
//       <div className="sticky top-0 z-10 flex flex-wrap items-center justify-between py-2 bg-white">
//         <Tabs defaultValue="all">
//           <TabsList>
//             <TabsTrigger value="all" className="text-sm px-4">
//               App Data
//             </TabsTrigger>
//             <TabsTrigger value="read" className="text-sm px-4">
//               Ads Data
//             </TabsTrigger>
//           </TabsList>
//         </Tabs>
//         <div className="flex gap-4">
//           <Select value={dateType} onValueChange={setDateType}>
//             <SelectTrigger className="w-[180px]">
//               <SelectValue />
//             </SelectTrigger>
//             <SelectContent>
//               <SelectItem value="LAST_7_DAYS">Last 7 Days</SelectItem>
//               <SelectItem value="THIS_MONTH">This Month</SelectItem>
//               <SelectItem value="LAST_MONTH">Last Month</SelectItem>
//               <SelectItem value="LAST_90_DAYS">Last 90 days</SelectItem>
//             </SelectContent>
//           </Select>
//           <Button size="sm" onClick={() => {}}>
//             Export
//             <IconAssets.Export />
//           </Button>
//         </div>
//       </div>
//       <AppDataContent />
//     </div>
//   );
// }

// const AppDataContent = () => {
//   return (
//     <div>
//       <DashboardKpi
//         data={{
//           downloadCount: {
//             count: '1000',
//             trend: 'up',
//             percentage: '10'
//           },
//           installAndOpenCount: {
//             count: '500',
//             trend: 'down',
//             percentage: '5'
//           },
//           registeredUserCount: {
//             count: '200',
//             trend: 'up',
//             percentage: '20'
//           },
//           activeUserCount: {
//             count: '100',
//             trend: 'up',
//             percentage: '10'
//           },
//           inactiveUserCount: {
//             count: '50',
//             trend: 'down',
//             percentage: '5'
//           }
//         }}
//         kpiData={[
//           {
//             label: 'Total Downloads',
//             value: 'downloadCount'
//           },
//           {
//             label: 'Install & Open',
//             value: 'installAndOpenCount'
//           },
//           {
//             label: 'Registered Users',
//             value: 'registeredUserCount'
//           },
//           {
//             label: 'Active Users',
//             value: 'activeUserCount'
//           },
//           { label: 'Inactive Users', value: 'inactiveUserCount' }
//         ]}
//       />
//     </div>
//   );
// };
