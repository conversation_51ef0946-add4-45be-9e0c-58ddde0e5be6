import { useEffect } from 'react';
import { Button } from '@/components/ui/button';
import beasyIcon from '@/asset/images/beasy-icon.png'; // Adjust the path as necessary
// import { useAuth } from '@/hooks/useAuth';
import { useForm } from 'react-hook-form';
import { signInSchema, type SignInSchemaProps } from '@/lib/schema/signin-schema';
import { zodResolver } from '@hookform/resolvers/zod';
import { Form } from '@/components/ui/form';
import FormInput from '@/components/common/form-input';
import { useAuth } from '@/hooks/use-auth';
const VITE_ENV = import.meta.env.VITE_ENV;

function LoginPage() {
  const { login, loginLoading } = useAuth();
  const signinForm = useForm<SignInSchemaProps>({
    mode: 'onSubmit',
    resolver: zodResolver(signInSchema),
    defaultValues:
      VITE_ENV === 'dev'
        ? {
            username: 'testing',
            password: '123456789'
          }
        : {
            username: '',
            password: ''
          }
  });

  useEffect(() => {
    console.log('LoginPage');
  }, []);

  const onSubmit = (values: SignInSchemaProps) => {
    login(values);
  };

  return (
    <div className="min-h-screen grid grid-cols-2">
      {/* Left Image Section */}
      <div className="col-span-1 items-center justify-center flex">
        <img
          src="./login.png"
          alt="Description of image"
          className="object-cover w-[584px] h-[784px]"
        />
      </div>

      {/* Right Login Form Section */}
      <div className="flex items-center justify-center p-8">
        <div className="w-[400px]">
          <img src={beasyIcon} alt="Logo" className="w-16 h-16 mx-auto" />
          <p className="text-2xl font-bold mt-4 mb-10 text-center">Log in to your Account</p>

          <Form {...signinForm}>
            <form onSubmit={signinForm.handleSubmit(onSubmit)} className="space-y-4">
              <div className="grid w-full items-center gap-4">
                <div className="flex flex-col space-y-1.5">
                  <FormInput
                    control={signinForm.control}
                    name="username"
                    label="Username"
                    placeholder="username"
                  />
                </div>
                <div className="flex flex-col space-y-1.5 relative">
                  <FormInput
                    control={signinForm.control}
                    name="password"
                    label="Password"
                    placeholder="Enter your password"
                    type="password"
                  />
                </div>
                {/* <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="rememberMe"
                      checked={rememberMe}
                      onCheckedChange={setRememberMe}
                    />
                    <Label htmlFor="rememberMe">Remember me</Label>
                  </div>
                  <Button variant="link" className="px-0">
                    Forgot Password
                  </Button>
                </div> */}
                <Button isLoading={loginLoading} type="submit" className="w-full mt-2">
                  Login
                </Button>
              </div>
            </form>
          </Form>
        </div>
      </div>
    </div>
  );
}

export default LoginPage;
