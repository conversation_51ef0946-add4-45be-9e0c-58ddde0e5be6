type UserManagementProps = {
  id: string; // Corresponds to NO.
  name: string; // User's name
  role: string;
  phoneNumber: string;
  status: 'Active' | 'Deactivated' | '';
  date: string; // Date string (e.g., "01 Jan, 2025")
};

type CategoryProps = {
  id: string;
  image: string;
  name: string;
  status: 'Active' | 'Inactive';
  date: Date;
};

type DraggableProps = {
  id: string;
  isNew: boolean;
};

type Task = {
  title: string;
  isExpanded: boolean;
  description: string;
} & DraggableProps;

type DraggableComboBoxProps = {
  data: { label: string; value: string }[];
  value: string;
} & DraggableProps;

type VariantProps = {
  id: string;
  image: string;
  name: string;
  price: string;
};

type CategoryAddOnProps = {
  id: string;
  image: string;
  name: string;
  status: string;
  variants: VariantProps[];
  date: Date;
};

type ProductProps = {
  id: string;
  name: string;
  category: CategoryProps[];
  status: string;
  date: Date;
};

type ServiceBundleProps = {
  id: string;
  name: string;
  status: 'Active' | 'In Active';
  bundleType: string;
  date: Date;
};

type ProductOptionProps = {
  id: string;
  name: string;
  price: string;
  status: string;
  date: Date;
};

type ReferralProgramProps = {
  id: string;
  name: string;
  email: string;
  referralCode: number;
  totalReferralUser: number;
  pointEarnd: number;
  date: Date;
};

type VoucherProps = {
  id: string;
  name: string;
  code: string;
  discountType: string;
  discountValue: string;
  usageLimit: string;
  peruserLimit: string;
  status: 'Active' | 'In Active';
  validFrom: Date;
  validTo: Date;
  eligibleServices: string;
  eligibleUsers: string;
};

type OrderComponentProps = {
  id: string;
  services: number;
  date: string;
  status: 'Pending' | 'In Progress' | 'Completed';
  imageSrc: string; // Image source URL
};

type ServiceDetailsProps = {
  id: string;
  serviceName: string;
  category: string;
  addOns: number;
  price: number;
  items: { id: string; name: string; checked: boolean }[];
  imageSrc: string;
};
type ServiceItem = {
  id: string;
  name: string;
  checked: boolean;
};
type PaginationDemoProps = {
  currentPage: number;
  totalPages: number;
};
type popupOptionProps = {
  id: string;
  title: string;
  description: string;
  confirmText: string;
  cancelText: string;
  inputPlaceholder: boolean;
  variant: 'default' | 'destructive' | 'success';
};

// real api

type CategoryAttributes = {
  iconUrl: string;
  id: string;
  nameEn: string;
  sort: number;
  status: boolean;
  updatedAt: string;
  products?: ProductAttributes[];
};

type ServiceAddOn = {
  id: string;
  product: string;
  variant: string;
  quantity: string;
};
type PaymentInformation = {
  id: string;
  paymentStatus: string;
  paymentMethod: string;
  note: string;
};

//api of order list

// type OrderItem = {
//   thumbnailUrl?: string;
//   hourCount?: number | null;
//   cleanerCount?: number | null;
//   floorCount?: number | null;
//   bedroomCount?: number | null;
//   amount?: number;
//   amountDisplay?: string;
// };

type PaginationProps = {
  pageSize: number;
  currentPage: number;
  totalItems: number;
  totalPages: number;
  onPageChange?: (page: number) => void;
};

type UserAddressProps = {
  address?: string;
  addressDetail?: string;
  latitude?: string;
  longitude?: string;
};

type ServiceItemDetailProps = {
  id: string;
  bulkOrderId: string;
  serviceItemId: string;
  serviceItemRemarks: string[];
};

type OrderListAttributes = {
  id?: string;
  bulkOrderId: string;
  customerLastName: string;
  customerFirstName: string;
  customerPhone: string;
  phone: string;
  email: string;
  note: string;
  UserAddress: UserAddressProps[];
  profileUrl: string;
  fullname: string;
  address: string;
  scheduleDate: string;
  scheduleStartDate: string;
  couponCode: string;
  amount: string;
  amountDisplay: string;
  discount: string;
  discountDisplay: string;
  serviceFee: string;
  serviceFeeDisplay: string;
  transportFee: string;
  transportFeeDisplay: string;
  subTotal: string;
  subTotalDisplay: string;
  vatFee: string;
  vatFeeDisplay: string;
  totalPayableAmount: string;
  totalPayableAmountDisplay: string;
  paymentMethod: string;
  status?: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'ACCEPTED' | 'CANCELLED';
  paymentStatus?: 'PENDING' | 'PAID' | 'UNPAID';
  amountAfterDiscountDisplay: string;
  paymentMethodDisplay: string;
  items?: ServicesProps[];
  thumbnailUrl?: string;
  totalCount?: number;
  latestCreatedAt?: string;
  // // ===============
  // finance
  category?: string;
  service?: string;
  serviceType?: string;
  address?: string;
  paymentDate?: string;
  customerRevaluation?: string;
  totalAmountDisplay?: string;
  netRevenue?: string;
  serviceItemDetails?: ServiceItemDetailProps[];
  cleaners?: CleanerAttributes[];
};

type ServicesProps = {
  categoryNameEn: string;
  categoryNameKm: string;
  categoryNameVi: string;
  categoryNameCn: string;
  categoryNameTw: string;
  thumbnailUrl: string;
  productOptionEn: string;
  productOptionKm: string;
  productOptionVi: string;
  productOptionCn: string;
  productOptionTw: string;
  hourCount: string;
  cleanerCount: string;
  floorCount: string;
  bedroomCount: string;
  amount: string;
  amountDisplay: string;
  addOns: AddOnsProps[];
};

type AddOnsProps = {
  id: string;
  nameEn: string;
  nameKm: string;
  nameVi: string;
  nameCn: string;
  nameTw: string;
  amount: string;
  qty: string;
  productAddOnId: string;
  amountDisplay: string;
};

type OrderCompleteServiceProps = {
  id: string;
  service_name: string;
  category: string;
  price: number;
  addOn: number;
  imageSrc: string;
  CompleteserviceDetails: ServiceDetailComplete[];
};
type InprogressServiceDetail = {
  description: string;
  quantity: number;
};

type ServiceDetailsInProgressProps = {
  id: string;
  serviceName: string;
  category: string;
  addOns: number;
  price: number;
  imageSrc: string;
  Serviceitems: InprogressServiceDetail[];
};

type FormData = {
  serviceDetails: InprogressServiceDetail[][];
};
type InprogressServiceDetail = {
  description: string;
  quantity: number;
};

type ServiceDetailsInProgressProps = {
  id: string;
  serviceName: string;
  category: string;
  addOns: number;
  price: number;
  imageSrc: string;
  Serviceitems: InprogressServiceDetail[];
};

type FormData = {
  serviceDetails: InprogressServiceDetail[][];
};

type TopupProps = {
  transactionId: string;
  topupAmount: number;
  paymentMethod: string;
  status: string;
  date: string;
  remark?: string;
};

type TopupProps = {
  transactionId: string;
  topupAmount: number;
  paymentMethod: string;
  status: string;
  date: string;
  remark?: string;
};

type BannerProps = {
  id: string;
  name: string;
  imageSrc: string;
  status: string;
  url: string;
};

type BannerProps = {
  id: string;
  name: string;
  imageSrc: string;
  status: string;
  url: string;
};
type PromotionProps = {
  id: string;
  name: string;
  applies_to: string;
  promotion_type: string;
  value: string;
  usage_limit: number;
  per_user_unit: number;
  status: string;
  created_by: string;
  valid_from: string;
  valid_to: string;
  eligible_service: string;
  eligible_users: string;
};
type FinanceProps = {
  id: string;
  orderId: string;
  customerName: string;
  profileUrl?: string;
  amount: string;
  discount: string;
  serviceFee: string;
  transportFee: string;
  vat: string;
  totalFee: string;
  status: string;
  date: string;
  remark?: string;
};
type RolesProps = {
  id: string;
  role: string;
  status: string;
  createdAt: Date;
  createdBy: string;
};
type TopupAttributes = {
  topUpID: string;
  customerName: string;
  transactionDate: string;
  paidAmount: number;
  customerPhone: string;
  credit: number;
  totalCredit: number;
  balanceDisplay: string;
  currentBalanceDisplay: string;
  paymentStatus: 'PENDING' | 'IN-REVIEW' | 'FAILED' | 'PAID' | 'REFUNDED';
};

type TopicProps = {
  id: string;
  name: string;
  topicName: string;
  createdAt: Date;
  updatedAt: Date;
};

type PushNotificationAttributes = {
  id: string;
  name: string;
  announcementTopics: TopicProps[];
  titleEn: string;
  titleKm: string;
  titleVi: string;
  titleCn: string;
  titleTw: string;
  contentEn: string;
  contentKm: string;
  contentVi: string;
  contentCn: string;
  contentTw: string;
  bannerEn: string;
  bannerKm: string;
  bannerVi: string;
  bannerCn: string;
  bannerTw: string;
  announcementType: string;
  status: string;
  startAt: string;
  createdAt: string;
  updatedAt: string;
};

type CleanerAttributes = {
  id: string;
  name: string;
  image?: string;
  gender?: string;
  status?: 1 | 0;
  createdAt?: string;
  updatedAt?: string;
  joinedDate?: string;
};

type ServiceItemAttributes = {
  id: string;
  name: string;
  description: string;
  status: boolean;
  createdAt: string;
  createdBy: string;
};

type CustomerServicesAttributes = {
  categoryId: string;
  nameEn: string;
  contactDate: string;
  customerServiceId: string;
  remark: string;
  status: string;
};

type CustomerAttributes = {
  id: string;
  name: string;
  createdAt: string;
  firstName: string;
  lastName: string;
  username: string;
  gender: string;
  dob: string;
  email: string;
  gender: string;
  balance: string;
  language: string;
  resourceReferral: string;
  customerServices: CustomerServicesAttributes;
};

type CouponAttributes = {
  id: string;
  name: string;
  code: string;
  promoTextEn: string;
  promoTextKm: string;
  promoTextVi: string;
  promoTextCn: string;
  promoTextTw: string;
  remark: string;
  status: string;
  value: string;
  type: string;
  effectiveDate: string;
  expiredDate: string;
  maxRedeemAmount: string;
  maxRedeemCount: string;
  maxRedeemAmountPax: string;
  maxRedeemCountPax: string;
  isNewUserOnly: string;
  isPreview: string;
  selectedProducts: string;
  createdAt: string;
  updatedAt: string;
};
