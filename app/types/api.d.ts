type PaymentStatusProps =
  | 'PENDING'
  | 'IN-REVIEW'
  | 'FAILED'
  | 'PAID'
  | 'REFUNDED'
  | 'PARTIALLY_PAID'
  | 'UNPAID';

type OrderTypeProps = 'ORDER' | 'DIRECT_SALE' | 'all';

type UserManagementProps = {
  id: string; // Corresponds to NO.
  name: string; // User's name
  role: string;
  phoneNumber: string;
  status: 'Active' | 'Deactivated' | '';
  date: string; // Date string (e.g., "01 Jan, 2025")
};

type CategoryProps = {
  id: string;
  image: string;
  name: string;
  status: 'Active' | 'Inactive';
  date: Date;
};

type DraggableProps = {
  id: string;
  isNew: boolean;
};

type Task = {
  title: string;
  isExpanded: boolean;
  description: string;
} & DraggableProps;

type DraggableComboBoxProps = {
  data: { label: string; value: string }[];
  value: string;
} & DraggableProps;

type VariantProps = {
  id: string;
  image: string;
  name: string;
  price: string;
};

type CategoryAddOnProps = {
  id: string;
  image: string;
  name: string;
  status: string;
  variants: VariantProps[];
  date: Date;
};

type ProductProps = {
  id: string;
  name: string;
  category: CategoryProps[];
  status: string;
  date: Date;
};

type ServiceBundleProps = {
  id: string;
  name: string;
  status: 'Active' | 'In Active';
  bundleType: string;
  date: Date;
};

type ProductOptionProps = {
  id: string;
  name: string;
  price: string;
  status: string;
  date: Date;
};

type ReferralProgramProps = {
  id: string;
  name: string;
  email: string;
  referralCode: number;
  totalReferralUser: number;
  pointEarnd: number;
  date: Date;
};

type VoucherProps = {
  id: string;
  name: string;
  code: string;
  discountType: string;
  discountValue: string;
  usageLimit: string;
  peruserLimit: string;
  status: 'Active' | 'In Active';
  validFrom: Date;
  validTo: Date;
  eligibleServices: string;
  eligibleUsers: string;
};

type OrderComponentProps = {
  id: string;
  services: number;
  date: string;
  status: 'Pending' | 'In Progress' | 'Completed';
  imageSrc: string; // Image source URL
};

type ServiceDetailsProps = {
  id: string;
  serviceName: string;
  category: string;
  addOns: number;
  price: number;
  items: { id: string; name: string; checked: boolean }[];
  imageSrc: string;
};
type ServiceItem = {
  id: string;
  name: string;
  checked: boolean;
};
type PaginationDemoProps = {
  currentPage: number;
  totalPages: number;
};
type popupOptionProps = {
  id: string;
  title: string;
  description: string;
  confirmText: string;
  cancelText: string;
  inputPlaceholder: boolean;
  variant: 'default' | 'destructive' | 'success';
};

// real api

type CategoryAttributes = {
  iconUrl: string;
  id: string;
  nameEn: string;
  sort: number;
  status: boolean;
  updatedAt: string;
  products?: { id: string; nameEn: string; productOptionV2s?: { id: string; nameEn: string }[] }[];
};

type ServiceAddOn = {
  id: string;
  product: string;
  variant: string;
  quantity: string;
};
type PaymentInformation = {
  id: string;
  paymentStatus: string;
  paymentMethod: string;
  note: string;
};

//api of order list

// type OrderItem = {
//   thumbnailUrl?: string;
//   hourCount?: number | null;
//   cleanerCount?: number | null;
//   floorCount?: number | null;
//   bedroomCount?: number | null;
//   amount?: number;
//   amountDisplay?: string;
// };

type PaginationProps = {
  pageSize: number;
  currentPage: number;
  totalItems: number;
  totalPages: number;
  onPageChange?: (page: number) => void;
};

type UserAddressProps = {
  id: number;
  address?: string;
  addressDetail?: string;
  latitude?: string;
  longitude?: string;
  isPrimary?: boolean;
};

type ServiceItemDetailProps = {
  id: string;
  bulkOrderId: string;
  serviceItemId: string;
  serviceItemRemarks: string[];
};

type OrderListAttributes = {
  vatNo?: string;
  id?: string;
  tranId?: string;
  bulkOrderId: string;
  status?: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'ACCEPTED' | 'CANCELLED';
  thumbnailUrl?: string;
  totalCount?: number;
  latestCreatedAt?: string;
  type: 'ORDER' | 'DIRECT_SALE';
  duration: number;

  customerLastName?: string;
  customerFirstName?: string;
  customerPhone?: string;
  phone?: string;
  email?: string;
  note?: string;
  userAddress?: UserAddressProps[];
  profileUrl?: string;
  fullname?: string;
  userId?: string;
  addressId?: number;
  address?: string;
  latitude?: string;
  longitude?: string;
  scheduleDate: string;
  scheduleStartDate: string;
  couponCode?: string;
  amount?: string;
  amountDisplay?: string;
  discount?: string;
  discountDisplay?: string;
  serviceFee: number;
  serviceFeeDisplay?: string;
  transportFee: number;
  transportFeeDisplay?: string;
  subTotal?: string;
  subTotalDisplay?: string;
  vatFee?: string;
  vatFeeDisplay?: string;
  totalPayableAmount?: string;
  totalPayableAmountDisplay?: string;
  paymentMethod?: string;
  paymentStatus?: PaymentStatusProps;
  amountAfterDiscountDisplay?: string;
  paymentMethodDisplay?: string;
  items?: ServicesProps[];

  // // ===============
  // finance
  category?: string;
  service?: string;
  serviceType?: string;
  address?: string;
  paymentDate?: string;
  paymentCompletedDate?: string;
  customerRevaluation?: string;
  totalAmountDisplay?: string;
  netRevenue?: string;
  serviceItemDetails?: ServiceItemDetailProps[];
  cleaners?: CleanerAttributes[];

  // DIRECT SALE

  deposit: number;
  depositDisplay?: string;
  remainingDisplay?: string;
  additionalFee: number;
  directSaleProduct?: string;
  directSaleProductOptionV2?: string;
  isPrivate?: boolean;
  reseller?: {
    id: string;
    firstName: string;
    lastName: string;
    username: string;
  };
  sale?: {
    id: string;
    firstName: string;
    lastName: string;
    username: string;
  };
  directSaleNextPaymentDate?: string;
  roomNum?: string;
  floorNum?: string;
  categoryId: string;
  receipts: {
    receiptUrl: string;
    id: string;
    status: string;
    bulkOrderId: string;
  }[];
};

type ServicesProps = {
  id?: string;
  categoryNameEn: string;
  productId: string;
  productEn: string;
  thumbnailUrl: string;
  productOptionId: string;
  productOptionEn: string;
  hourCount: string;
  cleanerCount: string;
  floorCount: string;
  bedroomCount: string;
  amount: string;
  amountDisplay: string;
  qty: string;
  addOns: AddOnsProps[];
};

type AddOnsProps = {
  id: string;
  nameEn: string;
  nameKm: string;
  nameVi: string;
  nameCn: string;
  nameTw: string;
  amount: string;
  qty: string;
  productAddOnId: string;
  amountDisplay: string;
};

type OrderCompleteServiceProps = {
  id: string;
  service_name: string;
  category: string;
  price: number;
  addOn: number;
  imageSrc: string;
  CompleteserviceDetails: ServiceDetailComplete[];
};
type InprogressServiceDetail = {
  description: string;
  quantity: number;
};

type ServiceDetailsInProgressProps = {
  id: string;
  serviceName: string;
  category: string;
  addOns: number;
  price: number;
  imageSrc: string;
  Serviceitems: InprogressServiceDetail[];
};

type FormData = {
  serviceDetails: InprogressServiceDetail[][];
};
type InprogressServiceDetail = {
  description: string;
  quantity: number;
};

type ServiceDetailsInProgressProps = {
  id: string;
  serviceName: string;
  category: string;
  addOns: number;
  price: number;
  imageSrc: string;
  Serviceitems: InprogressServiceDetail[];
};

type FormData = {
  serviceDetails: InprogressServiceDetail[][];
};

type TopupProps = {
  transactionId: string;
  topupAmount: number;
  paymentMethod: string;
  status: string;
  date: string;
  remark?: string;
};

type TopupProps = {
  transactionId: string;
  topupAmount: number;
  paymentMethod: string;
  status: string;
  date: string;
  remark?: string;
};

type BannerProps = {
  id: string;
  name: string;
  imageSrc: string;
  status: string;
  url: string;
};

type BannerProps = {
  id: string;
  name: string;
  imageSrc: string;
  status: string;
  url: string;
};
type PromotionProps = {
  id: string;
  name: string;
  applies_to: string;
  promotion_type: string;
  value: string;
  usage_limit: number;
  per_user_unit: number;
  status: string;
  created_by: string;
  valid_from: string;
  valid_to: string;
  eligible_service: string;
  eligible_users: string;
};
type FinanceProps = {
  id: string;
  orderId: string;
  customerName: string;
  profileUrl?: string;
  amount: string;
  discount: string;
  serviceFee: string;
  transportFee: string;
  vat: string;
  totalFee: string;
  status: string;
  date: string;
  remark?: string;
};
type RolesProps = {
  id: string;
  role: string;
  status: string;
  createdAt: Date;
  createdBy: string;
};
type TopupAttributes = {
  paymentMethod: string;
  customerId: string;
  topUpID: string;
  customerName: string;
  transactionDate: string;
  paidAmount: number;
  customerPhone: string;
  credit: number;
  totalCredit: number;
  balanceDisplay: string;
  currentBalanceDisplay: string;
  paymentStatus: PaymentStatusProps;
  tranId?: string;
};

type TopicProps = {
  id: string;
  name: string;
  topicName: string;
  createdAt: Date;
  updatedAt: Date;
};

type PushNotificationAttributes = {
  id: string;
  name: string;
  announcementTopics: TopicProps[];
  titleEn: string;
  titleKm: string;
  titleVi: string;
  titleCn: string;
  titleTw: string;
  contentEn: string;
  contentKm: string;
  contentVi: string;
  contentCn: string;
  contentTw: string;
  bannerEn: string;
  bannerKm: string;
  bannerVi: string;
  bannerCn: string;
  bannerTw: string;
  announcementType: string;
  status: string;
  startAt: string;
  createdAt: string;
  updatedAt: string;
};

type CleanerAttributes = {
  id: string;
  name: string;
  image?: string;
  gender?: string;
  status?: 1 | 0;
  createdAt?: string;
  updatedAt?: string;
  expertiseIds?: string[];
  joinedDate?: string;
};

type ServiceItemAttributes = {
  id: string;
  name: string;
  description: string;
  status: boolean;
  createdAt: string;
  createdBy: string;
};

type CustomerServicesAttributes = {
  categoryId: string;
  nameEn: string;
  contactDate: string;
  customerServiceId: string;
  remark: string;
  status: string;
};

type CustomerAttributes = {
  id: string;
  name: string;
  createdAt: string;
  firstName: string;
  lastName: string;
  username: string;
  gender: string;
  dob: string;
  email: string;
  gender: string;
  balance: string;
  language: string;
  resourceReferral: string;
  totalOrders: string;
  totalOrderAmount: string;
  lastOrderDate: string;
  averageRatingRounded?: string;
  spenderCategory: string;
  lastContactDate?: string;
  remark?: string;
  customerServices: CustomerServicesAttributes;
};

type CouponAttributes = {
  id: string;
  name: string;
  code: string;
  promoTextEn: string;
  promoTextKm: string;
  promoTextVi: string;
  promoTextCn: string;
  promoTextTw: string;
  remark: string;
  status: string;
  value: number;
  type: string;
  effectiveDate: string;
  expiredDate: string;
  minSpentAmount: number;
  maxRedeemAmount: number;
  maxRedeemCount: string;
  maxRedeemAmountPax: string;
  maxRedeemCountPax: string;
  isNewUserOnly: string;
  isPreview: string;
  selectedProducts: string[];
  selectedOptions: string[];
  createdAt: string;
  updatedAt: string;
  transportFee: number;
  serviceFee: number;
  targetUser: string;
  userCount: string;
  users: {
    id: string;
    usageCount: string;
    firstName: string;
    lastName: string;
    username: string;
    qty: number;
  }[];
};

type BlockedScheduleAttributes = {
  iconUrl?: string;
  id?: string;

  date: string;
  labelEn: string;
  labelKm: string;
  labelCn: string;
  labelTw: string;
  labelVi: string;

  titleEn: string;
  titleKm: string;
  titleVi: string;
  titleCn: string;
  titleTw: string;

  messageEn: string;
  messageKm: string;
  messageVi: string;
  messageCn: string;
  messageTw: string;

  categoryIds: number[];
  createdAt?: string;
  updatedAt?: string;
};

type OtpAttributes = {
  clientIp: string;
  createdAt: string;
  deviceId: string;
  expDate: string;
  id: string;
  isVerified: string;
  otp: string;
  status: string;
  updatedAt: string;
  username: string;
};
type PaymentLinkAttributes = {
  id: string;
  bulkOrderId: string;
  title: string;
  effectiveDate: string;
  expireDate: string;
  type: string;
  tranId: string;
  tranInitDate: string;
  amount: string;
  vat: string;
  status: PaymentStatusProps;
  customData: {
    qty: number;
    couponId: number;
  }[];
  createdAt: string;
  updatedAt: string;
  userId: number;
  user: {
    id: number;
    username: string;
    firstName: string;
    lastName: string;
    balance: string;
    createdAt: string;
  };
};

type ProductAttributes = {
  id: string;
  nameEn: string;
  nameKm: string;
  nameVi: string;
  nameCn: string;
  nameTw: string;
  taskInfoEn: string;
  taskInfoKm: string;
  taskInfoCn: string;
  taskInfoTw: string;
  taskInfoVi: string;
  iconUrl: string;
  isSelected: boolean;
  hasQty: boolean;
};

type ServiceServiceTypeAttrubutes = (ProductAttributes & {
  productOptionV2s: ProductAttributes[];
})[];

type UserAttributes = {
  balance: string;
  count: string;
  createdAt: string;
  description: string;
  deviceId: string;
  dob: string;
  email: string;
  fcmToken: string;
  firstName: string;
  gender: string;
  id: string;
  isBlacklist: boolean;
  isTest: boolean;
  language: string;
  lastName: string;
  loginType: string;
  password: string;
  phoneModel: string;
  phoneType: string;
  profileUrl: string;
  referralCode: string;
  referrerCode: string;
  socialId: string;
  status: string;
  type: string;
  updatedAt: string;
  username: string;
};

type AddressAttributes = {
  id: string;
  isPrimary: string;
  name: string;
  address: string;
  addressDetail: string;
  floorNum: string;
  roomNum: string;
  note: string;
  latitude: string;
  longitude: string;
  sort: string;
  bookingDate: string;
};

type ProductOptionsAttributes = {
  amount: string;
  amountAddOn: string;
  bedroomCount: string;
  cleanerCount: string;
  discount: string;
  floorCount: string;
  hourCount: string;
  iconUrl: string;
  id: string;
  isPrimary: string;
  nameCn: string;
  nameEn: string;
  nameKm: string;
  nameTw: string;
  nameVi: string;
  productId: string;
  productNameCn: string;
  productNameEn: string;
  productNameKm: string;
  productNameTw: string;
  productNameVi: string;
  qty: string;
  subTotal: string;
  type: string;
};

type ABAPaymentCheckProps = {
  apv: string;
  discount_amount: string;
  original_amount: string;
  payment_amount: string;
  payment_currency: string;
  payment_status: 'PENDING' | 'IN-REVIEW' | 'FAILED' | 'PAID' | 'REFUNDED';
  payment_status_code: string;
  refund_amount: string;
  total_amount: string;
  transaction_date: string;
};
type TrendProps = {
  count: string;
  trend: 'down' | 'up' | 'none';
  percentage: string;
};

type KeyValueProps = {
  [key: string]: number;
};

type OverviewChartProps = {
  // currentRows: KeyValueProps;
  // prevRows: KeyValueProps;
  chartData: {
    date: string;
    label: string;
    thisMonth: number;
    lastMonth: number;
    tooltipLabel: string;
  }[];
  currentRange: { from: Date; to: Date };
  prevRange: { from: Date; to: Date };
};

type LabelValueProps = {
  label: string;
  value: number;
};

type ServiceBreakdownProps = {
  iconUrl: string;
  category: string;
  totalWithVat: number;
  createdAt: string;
  orderCount: string;
  updatedAt: string;
};

type CustomerRatingProps = {
  createdAt: string;
  feedback: string;
  star: string;
};

type DirectSaleUserProps = {
  id: string;
  username: string;
  firstName: string;
  lastName: string;
};

// type DirectSaleCustomerAttributes = {
//   id: string;
//   firstName: string;
//   lastName: string;
//   username: string;
//   createdAt: string;
//   language: string;
//   resourceReferral: string;
//   // totalOrders: string;
//   totalOrderAmount: string;
//   // remark?: string;
//   customerServices?: CustomerServicesAttributes;
// };
