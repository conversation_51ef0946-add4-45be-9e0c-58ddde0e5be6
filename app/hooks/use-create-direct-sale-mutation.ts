import api from '@/api/api';
import { API_ENDPOINT } from '@/api/endpoint';
import { QUERY_KEY_ENUM } from '@/constants/query-key-enum';
import { useMutation } from '@tanstack/react-query';
import customQueryClient from './use-custom-query-client';

// type PayloadProps = {
//   userId: unknown;
//   qty: unknown;
//   amount: unknown;
//   categoryType: unknown;
//   directSaleProduct: unknown;
//   directSaleProductOptionV2: unknown;
//   productOptionIdV2?: string;
//   isPrimary: boolean;
//   customerFirstName: unknown;
//   customerLastName: unknown;
//   customerPhone: unknown;
//   paymentMethod: unknown;
//   note: unknown;
//   scheduleStartDate: unknown;
//   address: unknown;
//   addressId: unknown;
//   transportFee: unknown;
//   status: unknown;
//   paymentStatus: unknown;
//   additionalFee: unknown;
//   deposit: unknown;
// };

export default function useCreateDirectSaleMutation(bulkOrderId?: string) {
  const apiFn = (payload: FormData): Promise<string> => {
    return api.post(
      bulkOrderId
        ? `${API_ENDPOINT.UPDATE_DIRECT_SALE}${bulkOrderId}`
        : API_ENDPOINT.CREATE_DIRECT_ORDER,
      payload,
      {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      }
    );
  };

  const query = useMutation({
    mutationKey: [QUERY_KEY_ENUM.CREATE_DIRECT_ORDER, bulkOrderId],
    mutationFn: apiFn,
    onSuccess: () => {
      // toast.success('Cancelled with Refunded');
      customQueryClient.invalidateQueries({ queryKey: [QUERY_KEY_ENUM.ORDERS] });
      customQueryClient.invalidateQueries({ queryKey: [QUERY_KEY_ENUM.ORDER_DETAIL] });
      // navigate(-1);
    }
  });

  return query;
}
