import api from '@/api/api';
import { API_ENDPOINT } from '@/api/endpoint';
import { QUERY_KEY_ENUM } from '@/constants/query-key-enum';
import { useQuery } from '@tanstack/react-query';

export default function useCleanersQuery(status?: 1 | 0) {
  const apiFn = (): Promise<CleanerAttributes[]> => {
    return api.get(API_ENDPOINT.CLEANERS, {
      params: {
        status
      }
    });
  };

  const query = useQuery({
    queryKey: [QUERY_KEY_ENUM.CLEANERS],
    queryFn: apiFn
  });

  return query;
}
