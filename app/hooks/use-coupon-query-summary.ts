import api from '@/api/api';
import { API_ENDPOINT } from '@/api/endpoint';
import { QUERY_KEY_ENUM } from '@/constants/query-key-enum';
import { useQuery } from '@tanstack/react-query';

export default function useCouponListSummaryQuery() {
  const apiFn = (): Promise<CouponAttributes[]> => {
    return api.get(API_ENDPOINT.COUPON_LIST_SUMMARY);
  };

  const query = useQuery({
    queryKey: [QUERY_KEY_ENUM.COUPON_LIST_SUMMARY],
    queryFn: apiFn
  });

  return query;
}
