import api from '@/api/api';
import { API_ENDPOINT } from '@/api/endpoint';
import { QUERY_KEY_ENUM } from '@/constants/query-key-enum';
import { useMutation } from '@tanstack/react-query';
import { toast } from 'sonner';
import customQueryClient from './use-custom-query-client';

export function useUpdatePaymentStatusMutation(bulkOrderId: string) {
  const apiFn = ({ image, status }: { image: File; status: string }): Promise<Blob> => {
    const formData = new FormData();

    formData.append('imgUrl', image);
    formData.append('status', status);

    return api.post(`${API_ENDPOINT.MARK_AS_PAID}${bulkOrderId}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  };

  return useMutation({
    mutationKey: [QUERY_KEY_ENUM.MARK_AS_PAID, bulkOrderId],
    mutationFn: apiFn,
    onSuccess: () => {
      toast.success('Successfully');
      customQueryClient.invalidateQueries({ queryKey: [QUERY_KEY_ENUM.ORDER_DETAIL] });
    }
  });
}
