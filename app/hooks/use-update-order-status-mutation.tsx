import api from '@/api/api';
import { API_ENDPOINT } from '@/api/endpoint';
import { QUERY_KEY_ENUM } from '@/constants/query-key-enum';
import { useMutation } from '@tanstack/react-query';
import { toast } from 'sonner';

export function useUpdateOrderStatusMutation() {
  const apiFn = ({
    bulkOrderId,
    status
  }: {
    bulkOrderId?: string;
    status?: 'ACCEPTED' | 'COMPLETED';
  }): Promise<{ message: string }> =>
    api.put(API_ENDPOINT.UPDATE_ORDER_STATUS, {
      status,
      bulkOrderId
    });

  return useMutation({
    mutationKey: [QUERY_KEY_ENUM.UPDATE_ORDER],
    mutationFn: apiFn,
    onSuccess: (res) => {
      toast.error(res?.message);
    }
  });
}
