import api from '@/api/api';
import { API_ENDPOINT } from '@/api/endpoint';
import { QUERY_KEY_ENUM } from '@/constants/query-key-enum';
import { useQuery } from '@tanstack/react-query';
import type { ColumnFiltersState } from '@tanstack/react-table';

export default function useCouponQuery({ columnFilters }: { columnFilters?: ColumnFiltersState }) {
  const apiFn = (): Promise<CouponAttributes[]> => {
    let params = {};
    if (columnFilters) {
      columnFilters.forEach((filter) => {
        params = {
          ...params,
          [filter.id]: filter.value
        };
      });
    }
    return api.get(API_ENDPOINT.COUPONS, {
      params
    });
  };

  const query = useQuery({
    queryKey: [QUERY_KEY_ENUM.COUPONS],
    queryFn: apiFn
  });

  return query;
}
