import api from '@/api/api';
import { API_ENDPOINT } from '@/api/endpoint';
import { QUERY_KEY_ENUM } from '@/constants/query-key-enum';
import { useQuery } from '@tanstack/react-query';

export default function useCouponQuery(status?: 1 | 0) {
  const apiFn = (): Promise<CouponAttributes[]> => {
    return api.get(API_ENDPOINT.COUPONS, {
      params: {
        status
      }
    });
  };

  const query = useQuery({
    queryKey: [QUERY_KEY_ENUM.COUPONS],
    queryFn: apiFn
  });

  return query;
}
