import api from '@/api/api';
import { API_ENDPOINT } from '@/api/endpoint';
import { QUERY_KEY_ENUM } from '@/constants/query-key-enum';
import { useQuery } from '@tanstack/react-query';

export default function useServiceItemsQuery(status?: string) {
  const apiFn = (): Promise<ServiceItemAttributes[]> => {
    let params = {};
    if (status) {
      params = {
        ...params,
        status
      };
    }
    return api.get(API_ENDPOINT.SERVICE_ITEMS, {
      params
    });
  };

  const query = useQuery({
    queryKey: [QUERY_KEY_ENUM.SERVICE_ITEMS],
    queryFn: apiFn
  });

  return query;
}
