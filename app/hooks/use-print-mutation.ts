import api from '@/api/api';
import { API_ENDPOINT } from '@/api/endpoint';
import { QUERY_KEY_ENUM } from '@/constants/query-key-enum';
import { useMutation } from '@tanstack/react-query';

export default function usePrintMutation() {
  const apiFn = (bulkOrderId: string): Promise<string> => {
    return api.get(API_ENDPOINT.PRINT, {
      params: {
        bulkOrderId
      },
      responseType: 'text' // make sure it's raw HTML
    });
  };

  return useMutation({
    mutationKey: [QUERY_KEY_ENUM.CREATE_SERVICE_ITEMS],
    mutationFn: apiFn,
    onSuccess: (html) => {
      // Create a hidden iframe
      const iframe = document.createElement('iframe');
      iframe.style.position = 'fixed';
      iframe.style.right = '0';
      iframe.style.bottom = '0';
      iframe.style.width = '0';
      iframe.style.height = '0';
      iframe.style.border = '0';

      document.body.appendChild(iframe);

      // Write HTML into iframe
      iframe.contentDocument?.open();
      iframe.contentDocument?.write(html);
      iframe.contentDocument?.close();

      // Trigger print once iframe is loaded
      iframe.onload = () => {
        iframe.contentWindow?.focus();
        iframe.contentWindow?.print();

        // Optional: cleanup after printing
        setTimeout(() => {
          document.body.removeChild(iframe);
        }, 1000);
      };
    }
  });
}
