import api from '@/api/api';
import { API_ENDPOINT } from '@/api/endpoint';
import { QUERY_KEY_ENUM } from '@/constants/query-key-enum';
import { useMutation } from '@tanstack/react-query';
import { toast } from 'sonner';
import customQueryClient from './use-custom-query-client';

type Props = {
  data: PushNotificationAttributes[];
  pagination: PaginationProps;
};

export default function useCancelOrderMutation() {
  const apiFn = (bulkOrderId: string): Promise<Props> => {
    return api.post(`${API_ENDPOINT.CANCEL_ORDER}${bulkOrderId}`);
  };

  const query = useMutation({
    mutationKey: [QUERY_KEY_ENUM.CANCEL_ORDER],
    mutationFn: apiFn,
    onSuccess: () => {
      toast.success('Cancelled with Refunded');
      customQueryClient.invalidateQueries({
        queryKey: [QUERY_KEY_ENUM.ORDERS]
      });
      customQueryClient.invalidateQueries({
        queryKey: [QUERY_KEY_ENUM.ORDER_DETAIL]
      });
      // navigate(-1);
    }
  });

  return query;
}
