import { useEffect } from 'react';
import numeral from 'numeral';
import useDirectSalePreviewMutation from './use-direct-sale-preview-mutation';
import { useWatch, type UseFormReturn } from 'react-hook-form';
import type { DirectSaleProps } from '@/lib/schema/direct-sale-schema';

type Props = {
  form: UseFormReturn<DirectSaleProps>;
  orderDetail?: OrderListAttributes;
};

export default function useDirectSaleEffect({ form, orderDetail }: Props) {
  const { control } = form;
  const categoryWatcher = useWatch({ control, name: 'category' });
  const servicesWatcher = useWatch({ control, name: 'services' });
  const pairServicesWatcher = useWatch({ control, name: 'pairServices' });
  const additionalFeeWatcher = useWatch({ control, name: 'additionalFee' });
  const serviceFeeWatcher = useWatch({ control, name: 'serviceFee' });
  const transportFeeWatcher = useWatch({ control, name: 'transportFee' });
  const depositWatcher = useWatch({ control, name: 'deposit' });
  const {
    data: directSalePreviewData,
    isPending: isPendingDirectSalePreview,
    mutate
  } = useDirectSalePreviewMutation();
  useEffect(() => {
    if (categoryWatcher && categoryWatcher !== 'OTHER') {
      mutate({ categoryId: categoryWatcher });
    }
    form.resetField('services', {
      defaultValue: {
        serviceId: '',
        serviceTypeId: '',
        quantity: '1',
        amount: ''
      }
    });
    form.resetField('pairServices', {
      defaultValue: []
    });
  }, [categoryWatcher, form, mutate]);

  useEffect(() => {
    if (categoryWatcher) {
      form.resetField('pairServices', {
        defaultValue: []
      });
    }
  }, [categoryWatcher, form]);

  useEffect(() => {
    if (orderDetail) {
      let payload: DirectSaleProps = {
        vatNo: orderDetail.vatNo || '',
        isEdit: true,
        reseller: orderDetail.reseller?.id ? `${orderDetail.reseller?.id}` : 'none',
        sale: orderDetail.sale?.id ? `${orderDetail.sale?.id}` : 'none',
        customerId: orderDetail.userId || '',
        // customerName: orderDetail.fullname || '',
        // phonenumber: orderDetail.phone || '',
        date: new Date(orderDetail.scheduleDate),
        address: orderDetail.addressId ? `${orderDetail.addressId}` : '',
        category: orderDetail.categoryId,
        nextPaymentDate: orderDetail.directSaleNextPaymentDate
          ? new Date(orderDetail.directSaleNextPaymentDate)
          : undefined,
        services: {
          serviceId: '',
          serviceTypeId: '',
          quantity: '1',
          amount: ''
        },
        paymentMethod: 'abapay_khqr_deeplink',
        paymentStatus: orderDetail.paymentStatus,
        deposit: `${orderDetail.deposit}`,
        additionalFee: orderDetail.additionalFee,
        transportFee: orderDetail.transportFee,
        serviceFee: orderDetail.serviceFee,
        remark: orderDetail.note,
        pairServices: []
      };

      if (!orderDetail.items) {
        form.reset(payload);
        return;
      }

      if (orderDetail.categoryId === 'OTHER') {
        payload = {
          ...payload,
          pairServices: orderDetail.items?.map((item) => ({
            id: `${item.id}`,
            serviceId: item.productId,
            serviceTypeId: item.productOptionId,
            quantity: `${item.qty}`,
            amount: `${item.amount}`
          })) as []
        };
      } else {
        payload = {
          ...payload,
          services: {
            id: `${orderDetail.items[0].id}`,
            serviceId: orderDetail.items[0].productId,
            serviceTypeId: orderDetail.items[0].productOptionId,
            quantity: `${orderDetail.items[0].qty}`,
            amount: `${orderDetail.items[0].amount}`
          }
        };
        for (let i = 1; i < orderDetail.items.length; i++) {
          const item = orderDetail.items[i];
          payload.pairServices?.push({
            id: `${item.id}`,
            serviceId: item.productId,
            serviceTypeId: item.productOptionId,
            quantity: `${item.qty}`,
            amount: `${item.amount}`
          } as never);
        }
      }
      console.log('asdlskd;sdkiajshndkasld;kasjhdkas', payload);
      form.reset(payload);
    }
  }, [form, orderDetail]);

  // const paymentMethodWatcher = useWatch({ control, name: 'paymentMethod' });
  // const userIdWatcher = useWatch({ control, name: 'sale' });
  // const addressWatcher = useWatch({ control, name: 'address' });
  // const serviceAddonWatcher = useWatch({ control, name: 'serviceAddOn' });

  useEffect(() => {
    const serviceFee = numeral(servicesWatcher?.amount || 0);
    pairServicesWatcher?.map((item) => {
      serviceFee.add(item.amount || 0);
    });
    serviceFee.add(additionalFeeWatcher);
    serviceFee.add(serviceFeeWatcher);
    serviceFee.add(transportFeeWatcher);
    // const subTotalAmount = serviceFee.value() || 0;
    // const deposit = numeral(depositWatcher).value() || 0;
    serviceFee.subtract(depositWatcher);

    const totalAmount = serviceFee.value() || 0;

    // if (deposit === 0) {
    //   form.setValue('paymentStatus', 'PENDING');
    // } else if (subTotalAmount > deposit) {
    //   form.setValue('paymentStatus', 'PARTIALLY_PAID');
    // } else {
    //   form.setValue('paymentStatus', 'PAID');
    // }

    form.setValue('totalPayableAmount', `${totalAmount < 0 ? 0 : totalAmount}`);
  }, [
    servicesWatcher,
    pairServicesWatcher,
    additionalFeeWatcher,
    serviceFeeWatcher,
    depositWatcher,
    transportFeeWatcher,
    form
  ]);

  return {
    directSalePreviewData,
    isPendingDirectSalePreview
  };
}
