import api from '@/api/api';
import { API_ENDPOINT } from '@/api/endpoint';
import { QUERY_KEY_ENUM } from '@/constants/query-key-enum';
import { useMutation } from '@tanstack/react-query';
import { useNavigate } from 'react-router';
import { toast } from 'sonner';

export function useAnnouncementMutation() {
  const navigate = useNavigate();

  const apiFn = (payload: {
    name: string;
    bannerCn?: File;
    bannerEn?: File;
    bannerKm?: File;
    bannerTw?: File;
    bannerVi?: File;
    titleEn: string;
    titleKm: string;
    titleVi: string;
    titleTw: string;
    titleCn: string;
    contentEn: string;
    contentKm: string;
    contentVi: string;
    contentTw: string;
    contentCn: string;
    topics: string[];
  }): Promise<Blob> => {
    const formData = new FormData();

    // Append normal fields
    formData.append('name', payload.name);
    formData.append('titleEn', payload.titleEn);
    formData.append('titleKm', payload.titleKm);
    formData.append('titleVi', payload.titleVi);
    formData.append('titleTw', payload.titleTw);
    formData.append('titleCn', payload.titleCn);
    formData.append('contentEn', payload.contentEn);
    formData.append('contentKm', payload.contentKm);
    formData.append('contentVi', payload.contentVi);
    formData.append('contentTw', payload.contentTw);
    formData.append('contentCn', payload.contentCn);
    formData.append('announcementType', 'NOW');

    // Append banners only if provided
    if (payload.bannerCn) formData.append('bannerCn', payload.bannerCn);
    if (payload.bannerEn) formData.append('bannerEn', payload.bannerEn);
    if (payload.bannerKm) formData.append('bannerKm', payload.bannerKm);
    if (payload.bannerTw) formData.append('bannerTw', payload.bannerTw);
    if (payload.bannerVi) formData.append('bannerVi', payload.bannerVi);

    // Append array items
    payload.topics.forEach((topic, i) => {
      formData.append(`topics[${i}]`, topic);
    });

    return api.post(API_ENDPOINT.CREATE_ANNOUNCEMENT, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  };

  return useMutation({
    mutationKey: [QUERY_KEY_ENUM.ANNOUNCEMENT],
    mutationFn: apiFn,
    onSuccess: () => {
      toast.success('Notification has been sent');
      navigate(-1);
    }
  });
}
