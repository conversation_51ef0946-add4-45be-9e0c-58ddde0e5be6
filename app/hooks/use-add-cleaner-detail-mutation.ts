import api from '@/api/api';
import { API_ENDPOINT } from '@/api/endpoint';
import { QUERY_KEY_ENUM } from '@/constants/query-key-enum';
import { useMutation } from '@tanstack/react-query';

export function useAddCleanerDetailMutation() {
  const apiFn = (payload: { bulkOrderId: string; cleanerId: string }): Promise<string> => {
    return api.post(API_ENDPOINT.ADD_CLEANER_DETAIL, {
      ...payload
    });
  };

  return useMutation({
    mutationKey: [QUERY_KEY_ENUM.ADD_CLEANER_DETAIL],
    mutationFn: apiFn
  });
}
