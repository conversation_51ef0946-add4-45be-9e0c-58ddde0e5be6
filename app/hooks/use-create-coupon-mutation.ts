import api from '@/api/api';
import { API_ENDPOINT } from '@/api/endpoint';
import { QUERY_KEY_ENUM } from '@/constants/query-key-enum';
import type { CouponSchemaProps } from '@/lib/schema/coupon-schema';
import { useMutation } from '@tanstack/react-query';
import { useNavigate } from 'react-router';
import { toast } from 'sonner';

type Props = {
  promoTextEn: string;
  promoTextKm: string;
  promoTextVi: string;
  promoTextTw: string;
  promoTextCn: string;
} & CouponSchemaProps;

export function useCreateCouponMutation(id?: string) {
  const navigate = useNavigate();

  const apiFn = (payload: Props): Promise<string> => {
    if (id !== 'new') {
      return api.post(API_ENDPOINT.UPDATE_COUPON, {
        ...payload,
        id
      });
    }

    return api.post(API_ENDPOINT.CREATE_COUPON, payload);
  };

  return useMutation({
    mutationKey: [QUERY_KEY_ENUM.CREATE_COUPON],
    mutationFn: apiFn,
    onSuccess: () => {
      toast.success('Created successfully');
      navigate(-1);
    }
  });
}
