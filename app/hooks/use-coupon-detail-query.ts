import api from '@/api/api';
import { API_ENDPOINT } from '@/api/endpoint';
import { QUERY_KEY_ENUM } from '@/constants/query-key-enum';
import { useQuery } from '@tanstack/react-query';

export default function useCouponDetailQuery(id?: string) {
  const apiFn = (): Promise<CouponAttributes> => {
    return api.get(`${API_ENDPOINT.COUPON_DETAIL}${id}`);
  };

  const query = useQuery({
    queryKey: [QUERY_KEY_ENUM.COUPON_DETAIL, id],
    queryFn: apiFn,
    enabled: id !== 'new'
  });

  return query;
}
