import { z } from 'zod';
import { fileSchema } from '../utils';

export const cleanerSchema = z.object({
  cleanerName: z.string().min(1, 'Required'),
  image: z.object({ file: fileSchema.optional(), url: z.string().optional() }).optional(),
  joinedDate: z.date(),
  gender: z.enum(['MALE', 'FEMALE']),
  status: z.enum(['Active', 'Inactive'])
});

export type CleanerSchemaProps = z.infer<typeof cleanerSchema>;
