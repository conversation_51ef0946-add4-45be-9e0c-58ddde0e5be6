import { z } from 'zod';
import { multiLangNameSchema } from './multi-lang-schema';

export const bannerSchema = z.object({
  name: multiLangNameSchema,
  status: z.enum(['Active', 'Inactive', 'Pending'], { required_error: 'Status is required' }),
  url: z.string().optional(),
  attachments: z.array(z.any({ required_error: 'Attachment is required' }))
});

export type BannerSchemaProps = z.infer<typeof bannerSchema>;
