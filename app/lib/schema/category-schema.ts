import { z } from 'zod';
import { multiLangNameSchema } from './multi-lang-schema';

const taskSchema = z.object({
  id: z.string().optional(),
  title: z.string().min(1, 'Title is required'),
  description: z
    .array(
      z.object({ id: z.string().optional(), value: z.string().min(1, 'Description is required') })
    )
    .min(1, 'Task information is required')
});

export const taskInformationSchema = z.object({
  en: taskSchema,
  km: taskSchema,
  vi: taskSchema,
  tw: taskSchema,
  cn: taskSchema
});

export const categorySchema = z.object({
  name: multiLangNameSchema,
  status: z.enum(['Active', 'Resigned']),
  attachments: z.array(z.string()),
  taskInformation: z
    .array(z.object({ id: z.string().optional(), value: taskInformationSchema }))
    .min(1, 'Task information is required')
});

export type CategorySchemaProps = z.infer<typeof categorySchema>;

export type TaskInformationSchemaProps = z.infer<typeof taskInformationSchema>;
