import { string, z } from 'zod';

export const directSaleScheme = z.object({
  customerName: string({ required_error: 'Required' }),
  phonenumber: string({ required_error: 'Required' }),
  date: string({ required_error: 'Required' }),
  address: string({ required_error: 'Required' }),
  category: string({ required_error: 'Required' }),
  service: string({ required_error: 'Required' }),
  serviceType: string({ required_error: 'Required' }),
  assignCleaner: string({ required_error: 'Required' })
});

export type DirectSaleProps = z.infer<typeof directSaleScheme>;
