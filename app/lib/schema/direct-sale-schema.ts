import { z } from 'zod';

const serviceSchema = z.object({
  id: z.string().optional(),
  serviceId: z.string().optional(),
  serviceTypeId: z.string().optional(),
  quantity: z.string().optional(),
  amount: z.string().optional()
});

export const directSaleScheme = z.object({
  // serviceAddOn: z.array(serviceSchema).optional(),
  // assignCleaner: z
  //   .array(
  //     z.object({
  //       id: z.string({ required_error: 'Required' }).min(1, 'Required'),
  //       name: z.string({ required_error: 'Required' })
  //     })
  //   )
  //   .optional(),
  vatNo: z.string().optional(),
  sale: z.string({ required_error: 'Required' }).min(1, 'Required'),
  reseller: z.string().optional(),
  customerId: z.string({ required_error: 'Required' }).min(1, 'Required'),
  // customerName: z.string({ required_error: 'Required' }).min(1, 'Required'),
  // phonenumber: z.string({ required_error: 'Required' }).min(1, 'Required'),
  date: z.coerce.date({ required_error: 'Required' }),
  address: z.string().min(1, 'Required'),
  category: z.string({ required_error: 'Required' }).min(1, 'Required'),
  services: serviceSchema,
  pairServices: z.array(serviceSchema).optional(),
  // payment
  // deposit: z.coerce.number().nonnegative(),
  deposit: z.string().min(1, 'Required'),
  additionalFee: z.coerce.number().nonnegative(),
  transportFee: z.coerce.number({ required_error: 'Required' }).nonnegative(),
  serviceFee: z.coerce.number({ required_error: 'Required' }).nonnegative(),
  paymentMethod: z.string({ required_error: 'Required' }).min(1, 'Required'),
  paymentStatus: z.string().optional(),
  // paymentDate: z.date({ required_error: 'Required' }),
  nextPaymentDate: z.date().optional(),
  markAsPrivate: z.boolean().optional(),
  serviceDetails: z.record(z.array(z.string())).optional(),
  totalPayableAmount: z.string().optional(),
  remark: z.string().optional(),
  file: z.any().optional(),
  isEdit: z.boolean().optional()
});

export type DirectSaleProps = z.infer<typeof directSaleScheme>;
export type ServiceSchemaProps = z.infer<typeof serviceSchema>;

// const addressSchema = z.object({
//   address: z.string({ required_error: 'Required' }).min(1, 'Required'),
//   latitude: z.string({ required_error: 'Required' }).min(1, 'Required'),
//   longitude: z.string({ required_error: 'Required' }).min(1, 'Required')
// });

// export type AddressSchemaProps = z.infer<typeof addressSchema>;
