import { z } from 'zod';
import { multiLangNameSchema } from './multi-lang-schema';

const customerSchema = z.object({
  id: z.string(),
  userId: z.string(),
  firstName: z.string(),
  lastName: z.string(),
  username: z.string(),
  qty: z.number()
});

export const couponSchema = z.object({
  name: z.string().min(1, 'Required'),
  code: z.string().min(1, 'Required'),
  promoText: multiLangNameSchema,
  value: z.string().min(1, 'Required'),
  type: z.string().min(1, 'Required'),
  isNewUserOnly: z.boolean(),
  targetUser: z.string().min(1, 'Required'),

  selectedProducts: z.array(z.string()).optional(),
  users: z.array(customerSchema).optional(),
  maxRedeemAmount: z.string().optional(),
  maxRedeemPerPax: z.string().optional(),
  maxRedeemTotal: z.string().optional(),
  remark: z.string().optional(),
  effectiveDate: z.date().optional(),
  expiredDate: z.date().optional()

  // "name": "XHS50",
  // "code": "XHS50",
  // "promoText": "50% off",
  // "remark": "",
  // "value": 0.50,
  // "type": "PERCENTAGE",
  // "effectiveDate": "2025-01-01T00:00:00Z",
  // "expiredDate": "2025-12-31T23:59:59Z",
  // "maxRedeemAmount": 100.00,
  // "maxRedeemPerPax": 1,
  // "maxRedeemTotal": 1000,
  // "isNewUserOnly": false
});

export type CouponSchemaProps = z.infer<typeof couponSchema>;
export type CustomerSchemaProps = z.infer<typeof customerSchema>;
