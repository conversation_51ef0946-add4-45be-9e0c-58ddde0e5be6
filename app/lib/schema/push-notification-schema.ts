import { z } from 'zod';
import { multiLangNameSchema } from './multi-lang-schema';
import { fileSchema } from '../utils';

export const topicSchema = z.object({
  id: z.string().optional(),
  userType: z.string().optional(),
  gender: z.string().optional(),
  age: z.string().optional()
});

// Base schema for shared fields
export const pushNotificationSchema = z.object({
  title: multiLangNameSchema,
  description: multiLangNameSchema,
  name: z.string().min(1, 'Required'),
  bannerEn: fileSchema.optional(),
  bannerKm: fileSchema.optional(),
  bannerVi: fileSchema.optional(),
  bannerCn: fileSchema.optional(),
  bannerTw: fileSchema.optional(),
  topics: z.array(topicSchema).min(1, 'Required')

  // pageUrl: z.string().optional(),
  // preferredService: z.string().min(1, 'Required'),
  // schedulerType: z.string().min(1, 'Required'),
  // startDate: z.string().min(1, 'Required'),
  // startTime: z.string().min(1, 'Required'),
  // endDate: z.string().min(1, 'Required'),
  // endTime: z.string().min(1, 'Required')
});

export type TopicSchemaProps = z.infer<typeof topicSchema>;
export type PushNotificationSchemaProps = z.infer<typeof pushNotificationSchema>;
