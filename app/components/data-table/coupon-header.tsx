import type { Table } from '@tanstack/react-table';
import SearchBar from '../common/search-bar';
import { Button } from '../ui/button';
import { Plus } from 'lucide-react';
import { NavLink } from 'react-router';

type Props = {
  table: Table<CouponAttributes>;
};

export default function CouponHeader({ table }: Props) {
  return (
    <div className="flex items-center p-4 justify-between">
      <SearchBar
        placeholder="Search for Coupon..."
        value={(table.getColumn('name')?.getFilterValue() as string) ?? ''}
        onChange={(val) => table.getColumn('name')?.setFilterValue(val)}
      />
      <div className="flex flex-row gap-4">
        <NavLink to="/coupon/new">
          <Button size="sm">
            New Coupon <Plus />
          </Button>
        </NavLink>
      </div>
    </div>
  );
}
