import type { Table } from '@tanstack/react-table';
import SearchBar from '../common/search-bar';
import { Button } from '../ui/button';
import { Plus } from 'lucide-react';
import { NavLink } from 'react-router';
import CustomSelect from '../common/custom-select';
import { usePermission } from '@/hooks/use-permission';
import { ACTIONS, MODULES } from '@/lib/permission';

type Props = {
  table: Table<CouponAttributes>;
};

export default function CouponHeader({ table }: Props) {
  const { hasPermission } = usePermission();

  return (
    <div className="flex items-center p-4 gap-4">
      <SearchBar
        placeholder="Search for Coupon..."
        value={(table.getColumn('name')?.getFilterValue() as string) ?? ''}
        onChange={(val) => table.getColumn('name')?.setFilterValue(val)}
      />
      <div className="w-[200px]">
        <CustomSelect
          data={[
            { label: 'All', value: 'ALL' },
            { label: 'Select', value: 'SELECTED' }
          ]}
          placeholder="Select Target User"
          value={(table.getColumn('targetUser')?.getFilterValue() as string) ?? ''}
          onValueChange={(val) => table.getColumn('targetUser')?.setFilterValue(val)}
        />
      </div>
      <div className="flex flex-1"></div>
      <div className="flex flex-row gap-4">
        {hasPermission(MODULES.MARKETING_COUPON, ACTIONS.ADD) && (
          <NavLink to="/coupon/new">
            <Button size="sm">
              New Coupon <Plus />
            </Button>
          </NavLink>
        )}
      </div>
    </div>
  );
}
