import SearchBar from '../common/search-bar';
import { useTranslation } from 'react-i18next';
import { Button } from '../ui/button';
import { useExportExcelMutation } from '@/hooks/use-export-excel-mutation';
import IconAssets from '@/asset/icons/icon-assets';
import DateRangePickerV2, { type DateRangePickerProps } from '../common/date-range-picker-v2';

type Props = {
  initialDateRange: { from?: Date; to?: Date };
  search: string;
  setSearch: (value: string) => void;
} & DateRangePickerProps;

export default function BComboHeader({ search, setSearch, ...rest }: Props) {
  const { t } = useTranslation();
  const { mutate } = useExportExcelMutation();

  const handleExportClicked = async () => {
    mutate({
      type: 'bcombo',
      startDate: rest.dateRange?.from?.toISOString() ?? '',
      endDate: rest.dateRange?.to?.toISOString() ?? ''
    });
  };
  return (
    <div className="flex items-center p-4 justify-between">
      <SearchBar
        placeholder={t('searchPlaceholder', 'Search for transactions id...')}
        value={search}
        onChange={setSearch}
      />

      <div className="flex flex-wrap items-center gap-4">
        <DateRangePickerV2 {...rest} />
        {/* Export Button */}
        <Button
          className="bg-[#1964AD] text-white hover:bg-[#3773ad]"
          variant="outline"
          size="sm"
          onClick={handleExportClicked}
        >
          {t('Export')}
          <IconAssets.Export />
        </Button>
      </div>
    </div>
  );
}
