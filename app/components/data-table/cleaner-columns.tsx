import { formatDate } from '@/lib/date-helper';
import type { ColumnDef, Row } from '@tanstack/react-table';
import ColumnUserInfo from '../common/column-user-info';
import { Badge } from '../ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '../ui/dropdown-menu';
import { Button } from '../ui/button';
import { MoreVertical, Pen } from 'lucide-react';
import { NavLink } from 'react-router';

export const cleanerColumns: ColumnDef<CleanerAttributes>[] = [
  {
    accessorKey: 'name',
    header: 'Name',
    cell: ({ row }) => <ColumnUserInfo name={row.original.name} image={row.original.image || ''} />
  },
  {
    accessorKey: 'gender',
    header: 'Gender',
    cell: ({ row }) => <div>{row.original.gender}</div>
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => (
      <Badge variant={row.original.status ? 'approve' : 'reject'}>
        {row.original.status ? 'Active' : 'Inactive'}
      </Badge>
    )
  },
  {
    accessorKey: 'joinedDate',
    header: 'Joined Date',
    cell: ({ row }) => (
      <div>{row.original.joinedDate ? formatDate(row.original.joinedDate) : '-'}</div>
    )
  },
  {
    id: 'actions',
    enableHiding: true,
    cell: ({ row }) => {
      return <Action row={row} />;
    }
  }
];

const Action = ({ row }: { row: Row<CleanerAttributes> }) => {
  return (
    <div className="flex justify-end">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <span className="sr-only">Open menu</span>
            <MoreVertical />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <NavLink to={`/cleaner/${row.original.id}`}>
            <DropdownMenuItem className="text-blue-500">
              Edit
              <Pen className="text-blue-500" />
            </DropdownMenuItem>
          </NavLink>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};
