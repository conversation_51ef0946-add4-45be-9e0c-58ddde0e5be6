import type { ColumnDef, Row } from '@tanstack/react-table';
import TableCellDiv from './table-cell-div';
import { formatDate } from '@/lib/date-helper';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '../ui/dropdown-menu';
import { Button } from '../ui/button';
import { MoreVertical, Pen } from 'lucide-react';
import { NavLink } from 'react-router';

export const couponColumns: ColumnDef<CouponAttributes>[] = [
  {
    accessorKey: 'name',
    header: 'Name',
    cell: ({ row }) => <TableCellDiv>{row.original.name}</TableCellDiv>
  },
  {
    accessorKey: 'code',
    header: 'Code',
    cell: ({ row }) => <TableCellDiv>{row.original.code}</TableCellDiv>
  },
  {
    accessorKey: 'value',
    header: 'Value',
    cell: ({ row }) => (
      <TableCellDiv>
        {row.original.type === 'PERCENTAGE'
          ? `${parseFloat(row.original.value) * 100}%`
          : `$${row.original.value}`}
      </TableCellDiv>
    )
  },
  {
    accessorKey: 'effectiveDate',
    header: 'Effective Date',
    cell: ({ row }) => (
      <TableCellDiv>
        {row.original.effectiveDate ? formatDate(row.original.effectiveDate) : '-'}
      </TableCellDiv>
    )
  },
  {
    accessorKey: 'expiredDate',
    header: 'Expired Date',
    cell: ({ row }) => (
      <TableCellDiv>
        {row.original.expiredDate ? formatDate(row.original.expiredDate) : '-'}
      </TableCellDiv>
    )
  },
  {
    accessorKey: 'selectedProducts',
    header: 'Selected Products',
    cell: ({ row }) => <TableCellDiv>{row.original.selectedProducts || '-'}</TableCellDiv>
  },
  {
    accessorKey: 'promoText',
    header: 'Promotion Text',
    cell: ({ row }) => <TableCellDiv>{row.original.promoTextEn || '-'}</TableCellDiv>
  },
  {
    accessorKey: 'remark',
    header: 'Remark',
    cell: ({ row }) => <TableCellDiv>{row.original.remark || '-'}</TableCellDiv>
  },
  {
    id: 'actions',
    enableHiding: true,
    cell: ({ row }) => {
      return <Action row={row} />;
    }
  }
];

const Action = ({ row }: { row: Row<CouponAttributes> }) => {
  return (
    <div className="flex justify-end">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <span className="sr-only">Open menu</span>
            <MoreVertical />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <NavLink to={`/coupon/${row.original.id}`}>
            <DropdownMenuItem className="text-blue-500">
              Edit
              <Pen className="text-blue-500" />
            </DropdownMenuItem>
          </NavLink>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};
