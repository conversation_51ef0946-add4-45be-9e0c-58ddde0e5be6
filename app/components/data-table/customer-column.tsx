import { formatDate, formatFullDate } from '@/lib/date-helper';
import type { ColumnDef, Row } from '@tanstack/react-table';
import { Calendar04Icon } from 'hugeicons-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import useCategoryNamesQuery from '@/hooks/use-category-name-query';
import { cn } from '@/lib/utils';
import { Input } from '../ui/input';
import { useEffect, useRef, useState } from 'react';
import { useUpdateCustomerServiceMutation } from '@/hooks/use-update-customer-service-mutation';
import customQueryClient from '@/hooks/use-custom-query-client';
import TableCellDiv from './table-cell-div';

export const customerColumns: ColumnDef<CustomerAttributes>[] = [
  // {
  //   accessorKey: 'Id',
  //   header: 'ID',
  //   cell: ({ row }) => <TableCellDiv className="!min-w-[20px]">{row.original.id}</TableCellDiv>
  // },
  {
    accessorKey: 'createdAt',
    header: 'Created At',
    cell: ({ row }) => (
      <TableCellDiv className="w-[195px]">{formatFullDate(row.original.createdAt)}</TableCellDiv>
    )
  },
  {
    accessorKey: 'username',
    header: 'Phone',
    cell: ({ row }) => <TableCellDiv className="w-[150px]">{row.original.username}</TableCellDiv>
  },
  {
    accessorKey: 'firstName',
    header: 'First Name',
    cell: ({ row }) => <TableCellDiv>{row.original.firstName}</TableCellDiv>
  },
  {
    accessorKey: 'lastName',
    header: 'Last Name',
    cell: ({ row }) => <TableCellDiv>{row.original.lastName}</TableCellDiv>
  },
  {
    accessorKey: 'dob',
    header: 'Date Of Birth',
    cell: ({ row }) => <TableCellDiv>{row.original.dob || '-'}</TableCellDiv>
  },
  {
    accessorKey: 'email',
    header: 'Email',
    cell: ({ row }) => <TableCellDiv>{row.original.email || '-'}</TableCellDiv>
  },
  {
    accessorKey: 'balance',
    header: 'Balance',
    cell: ({ row }) => (
      <TableCellDiv>{row.original.balance ? `$${row.original.balance}` : '-'}</TableCellDiv>
    )
  },
  {
    accessorKey: 'language',
    header: 'Language',
    cell: ({ row }) => (
      <TableCellDiv>
        {row.original.language ? getLanguage(row.original.language) : '-'}
      </TableCellDiv>
    )
  },
  {
    accessorKey: 'contactDate',
    header: 'Contact Date',
    cell: ({ row }) => (
      <TableCellDiv className="flex justify-between gap-6">
        <div>
          {row.original.customerServices?.contactDate
            ? formatDate(row.original.customerServices?.contactDate)
            : '-'}
        </div>
        <Calendar04Icon className="size-5" />
      </TableCellDiv>
    )
  },
  {
    accessorKey: 'resourceReferral',
    header: 'Resource Referral',
    cell: ({ row }) => <TableCellDiv>{row.original.resourceReferral || '-'}</TableCellDiv>
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => (
      <TableCellDiv>
        <CustomerServiceStatus row={row} />
      </TableCellDiv>
    )
  },
  {
    accessorKey: 'preferredService',
    header: 'Preferred Service',
    cell: ({ row }) => <PreferredService row={row} />
  },
  {
    accessorKey: 'description',
    header: 'Description',
    cell: ({ row }) => <Description row={row} />
  }
];

const getLanguage = (lang: string) => {
  const language = lang.toLowerCase();
  if (language === 'en') return 'English';
  if (language === 'km') return 'Khmer';
  if (language === 'vn') return 'Vietnamese';
  if (language === 'zh') return 'Chinese (Traditional)';
  if (language === 'cn') return 'Chinese (Simplified)';
  return '-';
};

const CustomerServiceStatus = ({ row }: { row: Row<CustomerAttributes> }) => {
  const { mutateAsync } = useUpdateCustomerServiceMutation(row.original.id);
  return (
    <Select
      defaultValue={row.original.customerServices?.status || 'none'}
      onValueChange={async (value) => {
        // handle update logic here (API call, state update, etc.)
        await mutateAsync({
          status: value
        });
        customQueryClient.invalidateQueries();
      }}
    >
      <SelectTrigger
        className={cn(
          'w-[230px] border-none shadow-none text-muted-foreground',
          row.original.customerServices?.status === 'NOT-INTEREST' &&
            'bg-red-500/20 text-red-500 font-semibold',
          row.original.customerServices?.status === 'INTEREST-BUT-NOT-NOW' &&
            'bg-blue-500/20 text-blue-500 font-semibold',
          row.original.customerServices?.status === 'NO-ANSWER' &&
            'bg-yellow-500/20 text-yellow-500 font-semibold',
          row.original.customerServices?.status === 'EXISTING-CUSTOMER' &&
            'bg-green-500/20 text-green-500 font-semibold'
        )}
      >
        <SelectValue placeholder="Select status" />
      </SelectTrigger>
      <SelectContent>
        <SelectItem className="text-muted-foreground" value="none">
          None
        </SelectItem>
        <SelectItem value="NOT-INTEREST">Not Interested</SelectItem>
        <SelectItem value="INTEREST-BUT-NOT-NOW">Interested But Not Now</SelectItem>
        <SelectItem value="NO-ANSWER">No Answer</SelectItem>
        <SelectItem value="EXISTING-CUSTOMER">Existing Customer</SelectItem>
      </SelectContent>
    </Select>
  );
};

const PreferredService = ({ row }: { row: Row<CustomerAttributes> }) => {
  const { data, isPending } = useCategoryNamesQuery();
  const [selected, setSelected] = useState<string>('none');
  const { mutateAsync } = useUpdateCustomerServiceMutation(row.original.id);

  useEffect(() => {
    if (row.original.customerServices?.categoryId) {
      setSelected(`${row.original.customerServices.categoryId}`);
    }
  }, [row.original.customerServices?.categoryId, data]);

  if (isPending) return <div>loading...</div>;

  return (
    <Select
      value={selected}
      onValueChange={async (value) => {
        setSelected(value);
        await mutateAsync({ categoryId: value });
      }}
    >
      <SelectTrigger className="w-[230px] border-none shadow-none">
        <SelectValue placeholder="Select status" />
      </SelectTrigger>
      <SelectContent>
        <SelectItem className="text-muted-foreground" value="none">
          None
        </SelectItem>
        {data?.map((item) => (
          <SelectItem key={`${item.id}`} value={`${item.id}`}>
            {item.nameEn}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
};

const Description = ({ row }: { row: Row<CustomerAttributes> }) => {
  const [description, setDescription] = useState<string>(row.original.customerServices?.remark);
  const handlerRef = useRef<NodeJS.Timeout>(null);
  const { mutateAsync } = useUpdateCustomerServiceMutation(row.original.id);

  return (
    <div className="w-[200px]">
      <Input
        value={description}
        onChange={(val) => {
          setDescription(val.target.value);

          if (handlerRef.current) {
            clearTimeout(handlerRef.current); // cleanup if description changes before 2s
          }
          handlerRef.current = setTimeout(async () => {
            console.log('API call with description:', val.target.value);
            await mutateAsync({ description: val.target.value });
          }, 1000);
        }}
      />
    </div>
  );
};
