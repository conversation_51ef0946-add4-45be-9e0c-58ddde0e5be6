import { formatDate } from '@/lib/date-helper';
import { getBadgePaymentStatusVariant, getPaymentStatusDisplayText } from '@/lib/utils';
import type { ColumnDef } from '@tanstack/react-table';
import { Badge } from '../ui/badge';

export const topupColumns: ColumnDef<TopupAttributes>[] = [
  // Transaction ID
  {
    accessorKey: 'id',
    header: 'Top Up ID',
    cell: ({ row }) => <div>{row.original.topUpID}</div>
  },
  {
    accessorKey: 'customerName',
    header: 'Customer Name',
    cell: ({ row }) => <div>{row.original.customerName}</div>
  },
  {
    accessorKey: 'phoneNumber',
    header: 'Phone Number',
    cell: ({ row }) => <div>{row.original.customerPhone}</div>
  },
  {
    accessorKey: 'transactionDate',
    header: 'Transaction Date',
    cell: ({ row }) => <div>{formatDate(row.original.transactionDate)}</div>
  },
  {
    accessorKey: 'paymentStatus',
    header: 'Payment Status',
    // cell: ({ row }) => <div>{row.original.paymentStatus}</div>
    cell: ({ row }) => {
      const status = row.original.paymentStatus;
      return (
        <Badge variant={getBadgePaymentStatusVariant(status)}>
          {getPaymentStatusDisplayText(status)}
        </Badge>
      );
    }
  },
  // Top-up Amount
  {
    accessorKey: 'amountPaid',
    header: 'Amount Paid (USD)',
    cell: ({ row }) => {
      return <div>{row.original.paidAmount}</div>;
    }
  },

  // // Payment Method
  // {
  //   accessorKey: 'totalCredit',
  //   header: 'Total Credit',
  //   cell: ({ row }) => {
  //     const amount = row.original.credit;
  //     return <div>{amount}</div>;
  //   }
  // },
  {
    accessorKey: 'balance',
    header: 'Balance',
    cell: ({ row }) => <div>{row.original.balanceDisplay}</div>
  },
  {
    accessorKey: 'currentBalance',
    header: 'Current Balance',
    cell: ({ row }) => <div>{row.original.currentBalanceDisplay}</div>
  }
];
