import SearchBar from '../common/search-bar';
import DateRangePickerV2, { type DateRangePickerProps } from '../common/date-range-picker-v2';
import { Button } from '../ui/button';
import type { Table } from '@tanstack/react-table';
import { NavLink } from 'react-router';
import { Plus } from 'lucide-react';

type Props = {
  initialDateRange: { from?: Date; to?: Date };
  table: Table<ServiceItemAttributes>;
} & DateRangePickerProps;

export default function ItemHeader({ table, ...rest }: Props) {
  return (
    <div className="flex items-center p-4 justify-between">
      <SearchBar
        placeholder="Search for cleaner..."
        value={(table.getColumn('name')?.getFilterValue() as string) ?? ''}
        onChange={(val) => table.getColumn('name')?.setFilterValue(val)}
      />

      <div className="flex flex-wrap items-center gap-4">
        <DateRangePickerV2 {...rest} />
        <NavLink to="/item/new">
          <Button size="sm">
            New Item
            <Plus />
          </Button>
        </NavLink>
      </div>
    </div>
  );
}
