import SearchBar from '../common/search-bar';
import DateRangePickerV2, { type DateRangePickerProps } from '../common/date-range-picker-v2';
import type { Table } from '@tanstack/react-table';
import CustomSelectApi from '../common/custom-select-api';
import { QUERY_KEY_ENUM } from '@/constants/query-key-enum';
import { API_ENDPOINT } from '@/api/endpoint';

type Props = {
  initialDateRange: { from?: Date; to?: Date };
  search: string;
  setSearch: (value: string) => void;
  table: Table<CustomerAttributes>;
} & DateRangePickerProps;

export default function RegisterCustomerHeader({ search, setSearch, table, ...rest }: Props) {
  return (
    <div className="flex items-start p-4 justify-between">
      <div className="flex flex-col gap-4">
        <SearchBar placeholder="Search phone, Name" value={search} onChange={setSearch} />
        <div className="flex gap-4">
          <CustomSelectApi
            apiConfig={{
              queryKey: QUERY_KEY_ENUM.CATEGORIES_NAME,
              pathUrl: API_ENDPOINT.CATEGORIES_NAME
            }}
            prefix="Category Name:"
            value={(table.getColumn('preferredService')?.getFilterValue() as string) ?? 'ALL'}
            onChange={(val) =>
              table.getColumn('preferredService')?.setFilterValue(val === 'ALL' ? undefined : val)
            }
          />
        </div>
      </div>

      <DateRangePickerV2 {...rest} />
    </div>
  );
}
