import { formatDate } from '@/lib/date-helper';
import type { ColumnDef } from '@tanstack/react-table';

export const pushNotificationColumns: ColumnDef<PushNotificationAttributes>[] = [
  // Name first

  {
    accessorKey: 'name',
    header: 'Name',
    cell: ({ row }) => <div>{row.original.name}</div>
  },
  {
    accessorKey: 'titleEn',
    header: 'Title',
    cell: ({ row }) => <div>{row.original.titleEn}</div>
  },
  {
    accessorKey: 'contentCn',
    header: 'Description',
    cell: ({ row }) => <div>{row.original.contentCn}</div>
  },
  // {
  //   accessorKey: 'announcementTopics',
  //   header: 'Target',
  //   cell: ({ row }) => (
  //     <div>{row.original.announcementTopics.map((item) => item.name).join(', ')}</div>
  //   )
  // },
  {
    accessorKey: 'bannerEn',
    header: 'Banner',
    cell: ({ row }) => (
      <div>
        {row.original.bannerEn ? (
          <img
            src={row.original.bannerEn}
            alt="Banner"
            className="w-12 h-12 rounded-full object-cover"
          />
        ) : (
          '-'
        )}
      </div>
    )
  },
  {
    accessorKey: 'announcementType',
    header: 'Type',
    cell: ({ row }) => <div>{row.original.announcementType}</div>
  },
  {
    accessorKey: 'startAt',
    header: 'Start Date',
    cell: ({ row }) => <div>{formatDate(row.original.startAt, true)}</div>
  },

  {
    accessorKey: 'updatedAt',
    header: 'Last Updated',
    cell: ({ row }) => <div>{formatDate(row.original.updatedAt, true)}</div>
  }
];
