import SearchBar from '../common/search-bar';
import DateRangePickerV2, { type DateRangePickerProps } from '../common/date-range-picker-v2';
import { useTranslation } from 'react-i18next';
import { Button } from '../ui/button';
import IconAssets from '@/asset/icons/icon-assets';
import { useExportExcelMutation } from '@/hooks/use-export-excel-mutation';
import { usePermission } from '@/hooks/use-permission';
import { ACTIONS, MODULES } from '@/lib/permission';

type Props = {
  search: string;
  setSearch: (value: string) => void;
} & DateRangePickerProps;

export default function FinanceHeader(props: Props) {
  const { search, setSearch, ...rest } = props;
  const { t } = useTranslation();
  const { mutate } = useExportExcelMutation();
  const { hasPermission } = usePermission();

  const handleExportClicked = async () => {
    mutate({
      startDate: rest.dateRange?.from?.toISOString() ?? '',
      endDate: rest.dateRange?.to?.toISOString() ?? ''
    });
  };

  return (
    <div className="flex items-center p-4 justify-between">
      {/* Search */}
      <SearchBar
        placeholder={t('searchPlaceholder', 'Search by Order ID or Customer...')}
        value={search}
        onChange={setSearch}
      />

      {/* Filters & Export */}
      <div className="flex flex-wrap items-center gap-4">
        {/* Date Range Picker */}
        <DateRangePickerV2 {...rest} />

        {/* Export Button */}

        {hasPermission(MODULES.FINANCE_ORDER, ACTIONS.EXPORT) && (
          <Button size="sm" onClick={handleExportClicked}>
            {t('Export')}
            <IconAssets.Export />
          </Button>
        )}
      </div>
    </div>
  );
}
