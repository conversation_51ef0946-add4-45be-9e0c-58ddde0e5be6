import { Select } from '../ui/select';
import SearchBar from '../common/search-bar';
import { Button } from '../ui/button';
import DateRangePickerV2, { type DateRangePickerProps } from '../common/date-range-picker-v2';
import { Plus } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { NavLink } from 'react-router';

type Props = {
  statusFilter: string;
  setStatusFilter: (value: string) => void;
  search: string;
  setSearch: (value: string) => void;
} & DateRangePickerProps;

export default function PushNotificationHeader({
  setStatusFilter,
  statusFilter,
  search,
  setSearch,
  ...rest
}: Props) {
  const { t } = useTranslation();
  return (
    <div className="flex items-center p-4 justify-between">
      <SearchBar placeholder="Search for notification..." value={search} onChange={setSearch} />
      <div className="flex flex-row gap-4">
        <Select value={statusFilter} onValueChange={setStatusFilter}></Select>
        <DateRangePickerV2 {...rest} />
        <NavLink to="/push-notification/new-push-notification">
          <Button size="sm">
            {t('header.newPushNotification')} <Plus />
          </Button>
        </NavLink>
      </div>
    </div>
  );
}
