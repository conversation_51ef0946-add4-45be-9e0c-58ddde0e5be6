import type { Table } from '@tanstack/react-table';
import SearchBar from '../common/search-bar';
import { Button } from '../ui/button';
import { Plus } from 'lucide-react';
import { NavLink } from 'react-router';

type Props = {
  table: Table<CleanerAttributes>;
};

export default function CleanerHeader({ table }: Props) {
  return (
    <div className="flex items-center p-4 justify-between">
      <SearchBar
        placeholder="Search for cleaner..."
        value={(table.getColumn('name')?.getFilterValue() as string) ?? ''}
        onChange={(val) => table.getColumn('name')?.setFilterValue(val)}
      />
      <div className="flex flex-row gap-4">
        <NavLink to="/cleaner/new">
          <Button size="sm">
            New Cleaner <Plus />
          </Button>
        </NavLink>
      </div>
    </div>
  );
}
