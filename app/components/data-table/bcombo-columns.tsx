import { formatDate } from '@/lib/date-helper';
import type { ColumnDef } from '@tanstack/react-table';

export const bcomboColumns: ColumnDef<TopupAttributes>[] = [
  // Transaction ID
  {
    accessorKey: 'id',
    header: 'Top Up ID',
    cell: ({ row }) => <div>{row.original.topUpID}</div>
  },
  {
    accessorKey: 'customerName',
    header: 'Customer Name',
    cell: ({ row }) => <div>{row.original.customerName}</div>
  },
  {
    accessorKey: 'phoneNumber',
    header: 'Phone Number',
    cell: ({ row }) => <div>{row.original.customerPhone}</div>
  },
  {
    accessorKey: 'transactionDate',
    header: 'Transaction Date',
    cell: ({ row }) => <div>{formatDate(row.original.transactionDate)}</div>
  },
  // Top-up Amount
  {
    accessorKey: 'amountPaid',
    header: 'Amount Paid (USD)',
    cell: ({ row }) => {
      return <div>{row.original.paidAmount}</div>;
    }
  },
  {
    accessorKey: 'bonusCredit',
    header: 'Bonus Credit (USD)',
    cell: ({ row }) => {
      const amount = row.original.credit;
      return <div>{amount}</div>;
    }
  },
  // Payment Method
  {
    accessorKey: 'totalCredit',
    header: 'Total Credit (USD)',
    cell: ({ row }) => {
      return <div>{row.original.totalCredit}</div>;
    }
  },
  {
    accessorKey: 'Balance',
    header: 'Balance',
    cell: ({ row }) => <div>{row.original.balanceDisplay}</div>
  },
  {
    accessorKey: 'currentBalance',
    header: 'Current Balance',
    cell: ({ row }) => <div>{row.original.currentBalanceDisplay}</div>
  }
];
