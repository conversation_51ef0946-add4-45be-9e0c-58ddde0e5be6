import { format } from 'date-fns';
import { useCalendarStore } from '@/store/calendar-store';
import OrderBlock from './order-block';

interface DayViewProps {
  currentDate: Date;
  onAddEvent: (date: Date) => void;
}

const HOURS = Array.from({ length: 13 }, (_, i) => i + 6); // 6 to 18

const COLUMNS_PER_ROW = 5; // configurable number of columns to display per row

function getLayout(orders: OrderListAttributes[], hourStart: number, hourEnd: number) {
  const hourOrders = orders.filter((order) => {
    const eventDate = new Date(order.scheduleStartDate);
    const eventHour = eventDate.getHours();
    const eventMinute = eventDate.getMinutes();
    const eventDuration = order.duration || 60;

    const eventStartInMinutes = eventHour * 60 + eventMinute;
    const eventEndInMinutes = eventStartInMinutes + eventDuration;
    const hourStartInMinutes = hourStart * 60;
    const hourEndInMinutes = hourEnd * 60;

    return eventStartInMinutes < hourEndInMinutes && eventEndInMinutes > hourStartInMinutes;
  });

  // console.log({ hourOrders });

  // Step 2: Remove duplicates (unique by bulkOrderId)
  const uniqueOrdersMap = new Map();
  for (const order of hourOrders) {
    if (!uniqueOrdersMap.has(order.bulkOrderId)) {
      uniqueOrdersMap.set(order.bulkOrderId, order);
    }
  }
  const uniqueOrders = Array.from(uniqueOrdersMap.values());

  // Step 3: Limit to first 5 unique orders
  const limitedOrders = uniqueOrders.slice(0, COLUMNS_PER_ROW);

  // Step 4: Create rows (each row has max 5 items)
  const rows = [];
  for (let i = 0; i < limitedOrders.length; i += COLUMNS_PER_ROW) {
    rows.push(limitedOrders.slice(i, i + COLUMNS_PER_ROW));
  }

  return { orders: uniqueOrders, rows };
}

export default function DayView({ currentDate, onAddEvent }: DayViewProps) {
  const { orders } = useCalendarStore();

  const dayOrders = orders.filter((order) => {
    const eventDate = new Date(order.scheduleStartDate);
    return (
      eventDate.getDate() === currentDate.getDate() &&
      eventDate.getMonth() === currentDate.getMonth() &&
      eventDate.getFullYear() === currentDate.getFullYear()
    );
  });

  return (
    <div className="p-6">
      <div className="mb-6 flex items-center justify-center">
        <h2 className="">{format(currentDate, 'EEE d')}</h2>
      </div>

      <div className="grid grid-cols-[100px_1fr] gap-0 border border-border rounded-lg overflow-hidden">
        <div className="bg-muted/30 border-r border-border">
          {HOURS.map((hour) => (
            <div
              key={`${hour}-col`}
              className="h-[80px] p-3 text-sm text-muted-foreground border-b border-border flex items-start justify-end"
            >
              {format(new Date(2024, 0, 1, hour), 'ha')}
            </div>
          ))}
        </div>

        <div className="relative">
          {(() => {
            const seen = new Set<string>(); // track shown orders for the day
            return HOURS.map((hour) => {
              const { rows } = getLayout(dayOrders, hour, hour + 1);
              const filteredRows = rows
                .map((row) =>
                  row.filter((order) => {
                    if (seen.has(order.bulkOrderId)) return false;
                    seen.add(order.bulkOrderId);
                    return true;
                  })
                )
                .filter((row) => row.length > 0);

              // compute height
              const maxRowHeight =
                filteredRows.length > 0
                  ? Math.max(...filteredRows.flat().map(() => (60 / 60) * 80))
                  : 80;

              return (
                <div
                  key={`${hour}-row`}
                  className="border-b border-border hover:bg-muted/50 cursor-pointer transition-colors relative"
                  style={{ height: `${maxRowHeight}px` }}
                  onClick={() => {
                    const eventDate = new Date(currentDate);
                    eventDate.setHours(hour, 0, 0, 0);
                    onAddEvent(eventDate);
                  }}
                >
                  <div className="absolute inset-0 flex flex-col gap-1">
                    {filteredRows.map((row, rowIndex) => (
                      <div
                        key={rowIndex}
                        className="flex space-x-2"
                        style={{ height: `${maxRowHeight}px` }}
                      >
                        {row.map((order) => (
                          <OrderBlock key={order.bulkOrderId} order={order} viewType={'day'} />
                        ))}
                      </div>
                    ))}
                  </div>
                </div>
              );
            });
          })()}
        </div>
      </div>
    </div>
  );
}

{
  /* Fill empty columns in the last row */
}
{
  /* {row.length < COLUMNS_PER_ROW &&
  Array.from({ length: COLUMNS_PER_ROW - row.length }).map((_, i) => (
    <div key={`empty-${i}`} style={{ width: `${100 / COLUMNS_PER_ROW}%` }} />
  ))} */
}
