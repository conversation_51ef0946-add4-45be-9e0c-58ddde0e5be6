import { format, startOfWeek, addDays } from 'date-fns';
import { useCalendarStore } from '@/store/calendar-store';
import OrderBlock from './order-block';

interface WeekViewProps {
  currentDate: Date;
  onAddEvent: (date: Date) => void;
}

const HOURS = Array.from({ length: 13 }, (_, i) => i + 6); // 6 to 18

function getLayout(orders: OrderListAttributes[], hourStart: number, hourEnd: number) {
  const hourOrders = orders.filter((order) => {
    const eventDate = new Date(order.scheduleStartDate);
    const eventHour = eventDate.getHours();
    const eventMinute = eventDate.getMinutes();
    const eventDuration = order.duration || 60;

    const eventStartInMinutes = eventHour * 60 + eventMinute;
    const eventEndInMinutes = eventStartInMinutes + eventDuration;
    const hourStartInMinutes = hourStart * 60;
    const hourEndInMinutes = hourEnd * 60;

    return eventStartInMinutes < hourEndInMinutes && eventEndInMinutes > hourStartInMinutes;
  });

  // Remove duplicates (unique by bulkOrderId)
  const uniqueOrdersMap = new Map();
  for (const order of hourOrders) {
    if (!uniqueOrdersMap.has(order.bulkOrderId)) {
      uniqueOrdersMap.set(order.bulkOrderId, order);
    }
  }
  const uniqueOrders = Array.from(uniqueOrdersMap.values());

  return uniqueOrders;
}

export default function WeekView({ currentDate, onAddEvent }: WeekViewProps) {
  const { orders } = useCalendarStore();

  // Get the start of the week (Sunday)
  const weekStart = startOfWeek(currentDate, { weekStartsOn: 0 });
  const weekDays = Array.from({ length: 7 }, (_, i) => addDays(weekStart, i));

  // Filter orders for each day of the week
  const getOrdersForDay = (date: Date) => {
    return orders.filter((order) => {
      const eventDate = new Date(order.scheduleStartDate);
      return (
        eventDate.getDate() === date.getDate() &&
        eventDate.getMonth() === date.getMonth() &&
        eventDate.getFullYear() === date.getFullYear()
      );
    });
  };

  return (
    <div className="p-6">
      {/* <div className="mb-6 flex items-center justify-center">
        <h2 className="text-lg font-semibold">
          {format(weekStart, 'MMM d')} - {format(addDays(weekStart, 6), 'MMM d, yyyy')}
        </h2>
      </div> */}

      <div className="grid grid-cols-[100px_repeat(7,1fr)] gap-0 border border-border rounded-lg overflow-hidden">
        {/* Time column header */}
        <div className="bg-muted/30 border-r border-b border-border p-3"></div>

        {/* Day headers */}
        {weekDays.map((day) => (
          <div
            key={day.toString()}
            className="bg-muted/30 border-r last:border-r-0 border-b border-border p-3 text-center"
          >
            <div className="font-semibold">{format(day, 'EEE')}</div>
            <div className="text-sm text-muted-foreground">{format(day, 'd')}</div>
          </div>
        ))}

        {/* Time labels */}
        <div className="bg-muted/30 border-r border-border">
          {HOURS.map((hour) => (
            <div
              key={hour}
              className="h-[80px] p-3 text-sm text-muted-foreground border-b border-border flex items-start justify-end"
            >
              {format(new Date(2024, 0, 1, hour), 'ha')}
            </div>
          ))}
        </div>

        {/* Week grid */}
        {weekDays.map((day) => {
          const dayOrders = getOrdersForDay(day);
          const seen = new Set<string>(); // track shown orders for the day

          return (
            <div key={day.toString()} className="relative border-r last:border-r-0 border-border">
              {HOURS.map((hour) => {
                const hourOrders = getLayout(dayOrders, hour, hour + 1);
                const filteredOrders = hourOrders.filter((order) => {
                  if (seen.has(order.bulkOrderId)) return false;
                  seen.add(order.bulkOrderId);
                  return true;
                });

                // compute height based on longest duration
                const maxRowHeight =
                  filteredOrders.length > 0
                    ? Math.max(...filteredOrders.map(() => (60 / 60) * 80))
                    : 80;

                return (
                  <div
                    key={hour}
                    className="border-b border-border hover:bg-muted/50 cursor-pointer transition-colors relative"
                    style={{ height: `${maxRowHeight}px` }}
                    onClick={() => {
                      const eventDate = new Date(day);
                      eventDate.setHours(hour, 0, 0, 0);
                      onAddEvent(eventDate);
                    }}
                  >
                    <div className="absolute inset-0 flex gap-1">
                      {filteredOrders.slice(0, 2).map((order, i) => {
                        if (filteredOrders.length > 2 && i !== 0) return null;
                        return (
                          <div key={order.bulkOrderId} className="flex-1 min-w-0">
                            <OrderBlock order={order} viewType="week" />
                          </div>
                        );
                      })}
                      {filteredOrders.length > 2 && (
                        <div className="flex-1 min-w-0">
                          <OrderBlock
                            order={orders[0]}
                            overideText={`+${filteredOrders.length} more`}
                            viewType="week"
                          />
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          );
        })}
      </div>
    </div>
  );
}
