import type { ViewType } from './advance-calendar';

interface EventBlockProps {
  order: OrderListAttributes;
  viewType: ViewType;
  overideText?: string;
}

export default function OrderBlock({ order, viewType, overideText }: EventBlockProps) {
  // console.log('OrderBlock: ', order);
  // const eventDate = new Date(order.scheduleStartDate);
  // const startHour = eventDate.getHours();
  // const startMinute = eventDate.getMinutes();
  // const duration = order.duration || 60;

  // const topPercent = (startMinute / 60) * 100;
  // const heightPercent = (duration / 60) * 100;

  const getEventColor = (type: string) => {
    switch (type) {
      case 'ORDER':
        return 'bg-primary/10 border-primary';
      default:
        return 'bg-destructive/10 border-destructive';
    }
  };

  let orderHeight = (60 / 60) * 80;

  switch (viewType) {
    case 'day':
    case 'week':
      orderHeight = ((order.duration * 60 || 60) / 60) * 80;
      break;

    case 'month':
      orderHeight = 50;
      break;

    default:
      break;
  }

  return (
    <div
      key={order.bulkOrderId}
      className={`flex-1 text-xs flex p-4 px-2 overflow-hidden border-l-8 ${getEventColor(order.type)}`}
      style={{
        height: `${orderHeight}px`,
        minHeight: '40px'
      }}
    >
      {overideText || `Order #${Number(order.bulkOrderId)}`}
    </div>
  );

  // return (
  //   <div
  //     className={`absolute left-1 right-1 rounded-md p-2 text-white text-xs font-medium overflow-hidden shadow-sm hover:shadow-md transition-shadow cursor-pointer ${getEventColor(
  //       order.type
  //     )}`}
  //     style={{
  //       top: `calc(${startHour * 60 + startMinute}px + ${topPercent}%)`,
  //       width: `${100 / columns}%`,
  //       height: `calc(${heightPercent}% + ${duration}px)`,
  //       minHeight: '30px'
  //     }}
  //   >
  //     {/* <div
  //     className={`rounded-md p-2 text-white font-medium overflow-hidden shadow-sm hover:shadow-md transition-shadow cursor-pointer ${getEventColor(
  //       order.type
  //     )}`}> */}
  //     <div className="font-semibold truncate">Order #{order.bulkOrderId}</div>
  //     {/* {order.candidate && <div className="text-xs opacity-90 truncate">{order.candidate}</div>}
  //     <div className="text-xs opacity-90">
  //       {format(eventDate, 'h:mm a')} ({duration}m)
  //     </div> */}
  //   </div>
  // );
}
