import { Button } from '../ui/button';
import { ChevronLeft } from 'lucide-react';
import { useNavigate } from 'react-router';

export default function OrderHeaderLeft() {
  const navigate = useNavigate();
  const handleBack = () => navigate(-1);

  return (
    <div className="flex flex-row items-center h-[88px]">
      <div className="flex flex-row items-center px-6 w-[400px] gap-4">
        <Button variant="ghost" size="icon" onClick={handleBack}>
          <ChevronLeft className="!size-6" />
        </Button>
        {/* <img src={beasyIcon} className="w-12 h-12" /> */}
        <p className="text-xl font-bold">Order Management</p>
      </div>
    </div>
  );
}
