import type { Table } from '@tanstack/react-table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import SearchBar from '../common/search-bar';
import { Button } from '../ui/button';
import { Plus } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { NavLink } from 'react-router';

type Props = {
  table: Table<BannerProps>;
  statusFilter: string;
  setStatusFilter: (value: string) => void;
  // If you want date range picker for banner, add props here
};

export default function BannerHeader({ table, setStatusFilter, statusFilter }: Props) {
  const { t } = useTranslation();

  return (
    <div className="flex items-center p-4 justify-between">
      <SearchBar
        placeholder={t('Search for banner...')}
        value={(table.getColumn('name')?.getFilterValue() as string) ?? ''}
        onChange={(val) => table.getColumn('name')?.setFilterValue(val)}
      />
      <div className="flex flex-row gap-4">
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder={t('Filter by status')} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">{t('All Status')}</SelectItem>
            <SelectItem value="Active">{t('Active')}</SelectItem>
            <SelectItem value="Inactive">{t('Inactive')}</SelectItem>
            <SelectItem value="Pending">{t('Pending')}</SelectItem>
          </SelectContent>
        </Select>

        {/* If you want date range picker, add here */}

        <NavLink to="/banner/new-banner">
          <Button size="sm">
            {t('header.newBanner')} <Plus />
          </Button>
        </NavLink>
      </div>
    </div>
  );
}
