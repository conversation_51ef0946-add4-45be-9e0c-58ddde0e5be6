import { cn } from '@/lib/utils';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '../ui/tabs';

type TabItem = { label: string; value: string };
type Props = {
  tabs: TabItem[];
  children: (tab: TabItem, index: number) => React.ReactNode;
};

export default function CustomTabs({ tabs, children }: Props) {
  return (
    <Tabs defaultValue="all" className="flex flex-1 relative overflow-auto">
      <TabsList className="ml-4 flex h-auto justify-start rounded-none bg-transparent p-0">
        {tabs.map((tab) => (
          <TabsTrigger
            key={tab.value}
            value={tab.value}
            className={cn(
              'relative px-4 py-3 font-normal text-muted-foreground transition-colors',
              'data-[state=active]:after:bg-blue-500 data-[state=active]:after:h-[2px] data-[state=active]:after:w-full',
              'data-[state=active]:after:absolute data-[state=active]:after:bottom-0 data-[state=active]:after:left-0',
              'data-[state=active]:shadow-none data-[state=active]:text-foreground data-[state=active]:font-bold'
            )}
          >
            {tab.label}
          </TabsTrigger>
        ))}
      </TabsList>
      <div className="flex flex-1 overflow-auto min-h-0 relative p-4">
        {tabs.map((tab, index) => (
          <TabsContent key={tab.value} value={tab.value}>
            {children(tab, index)}
          </TabsContent>
        ))}
      </div>
    </Tabs>
  );
}
