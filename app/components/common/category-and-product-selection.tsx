import useCategoryNamesQuery from '@/hooks/use-category-name-query';
import { Checkbox } from '../ui/checkbox';
import { Label } from '../ui/label';
import { useEffect, useState } from 'react';
import clsx from 'clsx';

export default function CategoryAndProductSelection({
  productIds,
  onProductIdsChange
}: {
  productIds?: string[];
  onProductIdsChange: (productIds: string[]) => void;
}) {
  const { data, isPending } = useCategoryNamesQuery(true);
  const [categoryIdsSelected, setCategoryIdsSelected] = useState<string[]>([]);

  const tempProductIds = productIds || [];

  // useEffect(()=>{
  //   data?
  // },[])

  if (isPending) return <div>loading...</div>;
  return (
    <div className="mt-6 space-y-6">
      {data?.map((item, index) => {
        return (
          <div key={index} className="flex flex-col">
            <div className="flex items-center gap-3">
              <Checkbox
                checked={categoryIdsSelected.indexOf(item.id) !== -1}
                onCheckedChange={() => {
                  if (categoryIdsSelected.indexOf(item.id) !== -1) {
                    setCategoryIdsSelected(categoryIdsSelected.filter((id) => id !== item.id));
                    onProductIdsChange(
                      tempProductIds.filter((id) => !item.products?.map((p) => p.id).includes(id))
                    );
                  } else {
                    setCategoryIdsSelected([...categoryIdsSelected, item.id]);
                  }
                }}
                id={item.nameEn}
              />
              <Label htmlFor={item.nameEn}>{item.nameEn}</Label>
            </div>
            <div
              className={clsx('pl-6 grid grid-cols-2 gap-4', {
                'mt-5': categoryIdsSelected.indexOf(item.id) !== -1
              })}>
              {(item.products || []).map((product, index) => (
                <div
                  key={index}
                  className={clsx('flex items-center gap-3', {
                    hidden: categoryIdsSelected.indexOf(item.id) === -1
                  })}>
                  <Checkbox
                    checked={tempProductIds.indexOf(product.id) !== -1}
                    onCheckedChange={() => {
                      if (tempProductIds.indexOf(product.id) !== -1) {
                        onProductIdsChange(tempProductIds.filter((id) => id !== product.id));
                      } else {
                        onProductIdsChange([...tempProductIds, product.id]);
                      }
                    }}
                    id={product.id}
                  />
                  <Label htmlFor={product.id}>{product.nameEn}</Label>
                </div>
              ))}
            </div>
          </div>
        );
      })}
    </div>
  );
}
