import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import type { DirectSaleProps } from '@/lib/schema/direct-sale-schema';
import { useWatch, type Control, type FieldArrayWithId } from 'react-hook-form';
import { FormField, FormItem } from '../ui/form';
import FormInput from './form-input';
import { Delete01Icon } from 'hugeicons-react';

type Props = {
  control: Control<DirectSaleProps>;
  fields: FieldArrayWithId<DirectSaleProps, 'pairServices', 'id'>[];
  onRemove: (index: number) => void;
  data?: ServiceServiceTypeAttrubutes;
  isLoading?: boolean;
};
export default function ServiceServiceTypeSelectionData({
  control,
  fields,
  onRemove,
  data
  // isLoading
}: Props) {
  // if (isLoading) return null;

  return fields.map((service, index) => (
    <div className="realtive col-span-1" key={service.id}>
      <ServiceItem index={index} control={control} onRemove={onRemove} data={data} canRemove />
    </div>
  ));
}

function ServiceItem({
  control,
  index,
  onRemove,
  data,
  canRemove
}: {
  control: Control<DirectSaleProps>;
  index: number;
  onRemove: (index: number) => void;
  data?: ServiceServiceTypeAttrubutes;
  canRemove: boolean;
}) {
  const selectedServiceId = useWatch({
    control,
    name: `pairServices.${index}.serviceId`
  });

  const selectedService = data?.find((item) => `${item.id}` === `${selectedServiceId}`);
  const productOptionV2s = selectedService?.productOptionV2s || [];

  return (
    <div className="grid grid-cols-4 gap-6 col-span-4">
      <FormField
        control={control}
        name={`pairServices.${index}.serviceId`}
        render={({ field }) => (
          <FormItem className="gap-x-0">
            <Select value={String(field.value)} onValueChange={field.onChange}>
              <SelectTrigger className="w-full truncate">
                <SelectValue placeholder="Select Service" />
              </SelectTrigger>
              <SelectContent>
                {(data || []).map((item) => (
                  <SelectItem key={item.id} value={String(item.id)}>
                    {item.nameEn}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </FormItem>
        )}
      />

      <ServiceOption1 control={control} data={productOptionV2s} index={index} />

      <FormInput control={control} name={`pairServices.${index}.quantity`} />
      <div className="flex items-center gap-4">
        <div className="w-full">
          <FormInput control={control} name={`pairServices.${index}.amount`} />
        </div>
        {canRemove && (
          <button type="button" onClick={() => onRemove(index)} className="text-destructive">
            <Delete01Icon size="20" />
          </button>
        )}
      </div>
    </div>
  );
}

const ServiceOption1 = ({
  control,
  data,
  index
}: {
  data?: ProductAttributes[];
  control: Control<DirectSaleProps>;
  index: number;
}) => {
  return (
    <FormField
      control={control}
      key={index}
      name={`pairServices.${index}.serviceTypeId`}
      render={({ field: serviceField }) => {
        return (
          <FormItem>
            <Select value={String(serviceField.value)} onValueChange={serviceField.onChange}>
              <SelectTrigger className="w-auto">
                <SelectValue placeholder="Select Service Type" />
              </SelectTrigger>
              <SelectContent>
                {(data || []).map((item) => (
                  <SelectItem key={item.id} value={String(item.id)}>
                    {item.nameEn}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </FormItem>
        );
      }}
    />
  );
};
