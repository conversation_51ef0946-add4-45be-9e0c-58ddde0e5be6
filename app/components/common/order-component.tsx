import { formatDate } from '@/lib/date-helper';
import React from 'react';
import { Badge } from '../ui/badge';
import { getBadgeStatusVariant, getStatusDisplayText } from '@/lib/utils';

type Props = {
  order: OrderListAttributes;
  isActive: boolean;
};

const OrderComponent: React.FC<Props> = (props) => {
  const { order } = props;

  return (
    <div
      className={`w-auto h-[74px] flex items-center justify-between p-3 bg-white 
        rounded-[8px] cursor-pointer
        ${props.isActive ? 'border-2 border-[#1964AD]' : 'border-2 border-transparent'}`}
    >
      <div className="flex items-center space-x-3 h-full">
        <img
          src={order.thumbnailUrl}
          alt={`Order ${order.bulkOrderId || ''} icon`}
          className="w-[48px] h-[48px] object-cover rounded-full"
        />

        <div className="flex flex-col h-full justify-between ">
          <h3 className="text-sm font-bold text-[#1A1A1A]">Order: {order.bulkOrderId || 'N/A'}</h3>
          <p className="text-xs font-medium text-[#707070]">
            {order.totalCount !== undefined ? `${order.totalCount} Services` : 'No service info'}
          </p>
        </div>
      </div>

      <div className="flex flex-col items-end justify-between h-full">
        <div className="flex flex-row">
          <Badge variant={getBadgeStatusVariant(order.status)} className="rounded-full h-6 px-2">
            <span className="text-xs">{getStatusDisplayText(order.status)}</span>
          </Badge>
          {/* <CheckIcon className="text-primary w-3 h-3" /> */}
        </div>

        {order.latestCreatedAt && (
          <span className="text-xs font-medium text-[#707070]">
            {formatDate(order.latestCreatedAt, true)}
          </span>
        )}
      </div>
    </div>
  );
};

export default OrderComponent;
