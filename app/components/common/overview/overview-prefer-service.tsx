import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  type ChartConfig
} from '@/components/ui/chart';
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell } from 'recharts';

const chartConfig = {
  Cleaning: {
    label: ' '
  },
  'Deep Cleaning': {
    label: ' '
  },
  'Office Cleaning': {
    label: ' '
  },
  Upholstery: {
    label: ' '
  }
} satisfies ChartConfig;

type Props = {
  data: LabelValueProps[];
};

const getColor = (type: string) => {
  // fill: var(--color-cleaning)
  // fill: var(--color-deep-cleaning)
  // fill: var(--color-office-cleaning)
  // fill: var(--color-upholstery)
  // fill: var(--color-ac-cleaning)
  const colors: { [key: string]: string } = {
    cleaning: 'var(--color-cleaning)',
    'deep-cleaning': 'var(--color-deep-cleaning)',
    'office-cleaning': 'var(--color-office-cleaning)',
    upholstery: 'var(--color-upholstery)',
    'ac-cleaning': 'var(--color-ac-cleaning)',
    'washing-machine': 'var(--color-washing-machine)',
    'pest-control': 'var(--color-pest-control)'
  };
  return colors[type];
};

export default function OverviewPreferService({ data }: Props) {
  const preferedServices = data.map((item) => ({
    ...item,
    fill: getColor(item.label.toLowerCase().replaceAll(' ', '-'))
  }));

  return (
    <Card className="lg:col-span-1 border h-[400px] py-4 gap-0 overflow-hidden">
      <CardHeader className="px-4 pb-4">
        <CardTitle>Preferred Service</CardTitle>
        <div className="flex justify-center">
          <ChartContainer config={chartConfig} className="w-[150px] h-[150px]">
            <PieChart width={150} height={150}>
              <ChartTooltip cursor={false} content={<ChartTooltipContent />} />
              <Pie
                data={preferedServices}
                nameKey="label"
                cx="50%"
                cy="50%"
                innerRadius={35}
                strokeWidth={5}
                paddingAngle={2}
                cornerRadius={4}
                dataKey="value"
              >
                {preferedServices.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.fill} />
                ))}
              </Pie>
            </PieChart>
          </ChartContainer>
        </div>
      </CardHeader>
      <CardContent className="flex flex-col flex-1 items-center p-0 overflow-scroll">
        <div className="flex flex-1 flex-col w-full px-6 gap-4">
          {preferedServices.map((item, index) => (
            <div key={index} className="flex items-center justify-between text-sm">
              <div className="flex items-center gap-2">
                <div
                  className="w-[6px] h-[6px] rounded-full"
                  style={{ backgroundColor: item.fill }}
                ></div>
                <span>{item.label}</span>
              </div>
              <span>{item.value}</span>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
