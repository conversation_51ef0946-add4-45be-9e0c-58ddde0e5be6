import { useProductOptionByProductIdQuery } from '@/hooks/use-products-product-option-query';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../ui/select';
import type { Control, FieldValues, Path } from 'react-hook-form';
import { FormField, FormItem } from '../../ui/form';

type Props<T extends FieldValues> = {
  control: Control<T>;
  serviceId?: string;
  name: Path<T>;
};

export default function ServiceOption<T extends FieldValues>({
  control,
  serviceId,
  name
}: Props<T>) {
  const { data } = useProductOptionByProductIdQuery(serviceId);

  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => {
        return (
          <FormItem className="col-span-1 relative">
            <Select
              value={String(field.value)}
              onValueChange={(val) => field.onChange(String(val))}
            >
              <SelectTrigger className="w-full truncate">
                <SelectValue placeholder="Select Service Type" />
              </SelectTrigger>
              <SelectContent className="">
                {(data || []).map((item) => (
                  <SelectItem className="" key={item.id} value={String(item.id)}>
                    {item.nameEn}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </FormItem>
        );
      }}
    />
  );
}
