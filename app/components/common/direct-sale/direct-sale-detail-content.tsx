import { But<PERSON> } from '@/components/ui/button';
import { DialogTitle } from '@/components/ui/dialog';
import { Plus } from 'lucide-react';
import { useFieldArray, useWatch, type Control } from 'react-hook-form';
import { type DirectSaleProps } from '@/lib/schema/direct-sale-schema';
import FormInput from '../form-input';
import { FormField, FormItem, FormLabel } from '../../ui/form';
import { DateTimePicker } from '../date-time-picker';
import AddressPicker from '../address-picker';
import CustomSelectApi from '../custom-select-api';
import { QUERY_KEY_ENUM } from '@/constants/query-key-enum';
import { API_ENDPOINT } from '@/api/endpoint';
import DatePicker from '../date-picker';
import CustomSelect from '../custom-select';
import { useDirectSaleUserQuery } from '@/hooks/use-direct-sale-user-query';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import ServiceOption from './service-option';
import ServiceServiceTypeSelectionData from '../service-service-type-selection-data';
import { Textarea } from '@/components/ui/textarea';
import ServiceInputBoxSelectionData from '../service-input-box-selection-data';
import { useMemo } from 'react';
import DirectSalePayment from './direct-sale-payment';
import { Uploader } from '../uploader';
import DirectSaleCustomerForm from './direct-sale-customer-form';

export default function DirectSaleDetailContent({
  control,
  data,
  isLoading,
  productData
}: {
  control: Control<DirectSaleProps>;
  data?: ServiceServiceTypeAttrubutes;
  productData: ProductAttributes[];
  isLoading?: boolean;
}) {
  const isEditWatcher = useWatch({ control, name: 'isEdit' });
  const categoryIdWatcher = useWatch({ control, name: 'category' });
  const servicesWatcher = useWatch({ control, name: 'services' });
  const paymentStatusWatcher = useWatch({ control, name: 'paymentStatus' });

  const { data: directSaleUserData } = useDirectSaleUserQuery();

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'pairServices'
  });

  const resellers = useMemo(() => {
    const temps = (directSaleUserData?.resellers || []).map((item) => ({
      label: `${item.firstName} ${item.lastName} (${item.username})`,
      value: item.id
    }));

    return [{ label: 'None', value: 'none' }].concat(temps);
  }, [directSaleUserData?.resellers]);

  return (
    <div className="flex flex-1 flex-col overflow-y-auto gap-6 px-2">
      <DialogTitle>Booking Information</DialogTitle>

      <div className="grid grid-cols-3 gap-6 mt-3">
        <FormField
          control={control}
          name="reseller"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>Select Reseller (Optional)</FormLabel>
              <CustomSelect
                placeholder="Select Reseller (Optional)"
                data={resellers}
                value={field.value}
                onValueChange={field.onChange}
              />
            </FormItem>
          )}
        />
        <FormField
          control={control}
          name="sale"
          render={({ field }) => (
            <FormItem className="flex flex-col">
              <FormLabel>Select Sale</FormLabel>
              <CustomSelect
                placeholder="Select Sale"
                data={(directSaleUserData?.users || []).map((item) => ({
                  label: `${item.firstName} ${item.lastName} (${item.username})`,
                  value: item.id
                }))}
                value={field.value}
                onValueChange={field.onChange}
              />
            </FormItem>
          )}
        />

        <AddressPicker control={control} />

        <DirectSaleCustomerForm control={control} />
        <FormField
          control={control}
          name="date"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Date</FormLabel>
              <DateTimePicker
                title="Schedule Date"
                buttonText="Apply"
                haveIcon
                date={field.value}
                onChange={field.onChange}
              />
              {/* <FormMessage /> */}
            </FormItem>
          )}
        />
        {/* <FormField
          control={control}
          name="assignCleaner"
          render={({ field }) => (
            <FormItem>
              <Label>Assign Cleaner</Label>
              <AssignCleaner
                orderCleaners={field.value}
                onChange={(cleaner) => {
                  // if have inside remove else insert
                  if (field.value?.some((item) => item.id === cleaner.id)) {
                    field.onChange(field.value.filter((item) => item.id !== cleaner.id));
                    return;
                  }
                  field.onChange([...(field.value || []), cleaner]);
                }}
              />
            </FormItem>
          )}
        /> */}
        <FormInput
          control={control}
          name="vatNo"
          label="VAT No (Optional)"
          placeholder="VAT No (Optional)"
        />
        <FormField
          control={control}
          name="category"
          render={({ field }) => (
            <FormItem className="col-span-1 gap-x-0">
              <FormLabel>Category</FormLabel>
              <CustomSelectApi
                other
                disabledAll
                placeholder="Select Category"
                apiConfig={{
                  queryKey: QUERY_KEY_ENUM.CATEGORIES_NAME,
                  pathUrl: API_ENDPOINT.CATEGORIES_NAME
                }}
                value={field.value}
                onChange={field.onChange}
              />
              {/* <FormMessage /> */}
            </FormItem>
          )}
        />

        {categoryIdWatcher && (
          <FormItem className="col-span-3 grid grid-cols-4 gap-x-6">
            <FormLabel>Service</FormLabel>
            <FormLabel>Service Type</FormLabel>
            <FormLabel>Quantity</FormLabel>
            <FormLabel>Total Amount</FormLabel>

            {categoryIdWatcher === 'OTHER' ? (
              <>
                <ServiceInputBoxSelectionData control={control} fields={fields} onRemove={remove} />

                <div className="col-span-4 flex justify-end">
                  <Button
                    variant="link"
                    type="button"
                    onClick={() => {
                      append({
                        serviceId: '',
                        serviceTypeId: '',
                        quantity: '1',
                        amount: ''
                      });
                    }}
                  >
                    <Plus />
                    Add More Service
                  </Button>
                </div>
              </>
            ) : (
              <>
                <FormField
                  control={control}
                  name="services"
                  render={({ field }) => {
                    return (
                      <FormItem className="grid grid-cols-4 col-span-4 gap-x-6">
                        <div className="col-span-1">
                          <Select
                            value={field.value.serviceId?.toString()}
                            onValueChange={(val) =>
                              field.onChange({ ...field.value, serviceId: `${val}` })
                            }
                          >
                            <SelectTrigger className="w-full truncate">
                              <SelectValue placeholder="Select Service" />
                            </SelectTrigger>
                            <SelectContent>
                              {(productData || []).map((item) => (
                                <SelectItem key={item.id} value={String(item.id)}>
                                  {item.nameEn}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                        <ServiceOption
                          serviceId={field.value.serviceId}
                          name="services.serviceTypeId"
                          control={control}
                        />

                        <div className="col-span-1">
                          <FormInput
                            displayMessage={false}
                            control={control}
                            name="services.quantity"
                          />
                        </div>
                        <div className="col-span-1">
                          <FormInput control={control} name={`services.amount`} />
                        </div>
                      </FormItem>
                    );
                  }}
                />
                {servicesWatcher.serviceId && (
                  <>
                    <div className="col-span-4">
                      <ServiceServiceTypeSelectionData
                        control={control}
                        fields={fields}
                        onRemove={remove}
                        data={data}
                        isLoading={isLoading}
                      />
                    </div>

                    <div className="col-span-4 flex justify-end">
                      <Button
                        variant="link"
                        type="button"
                        onClick={() => {
                          append({
                            serviceId: '',
                            serviceTypeId: '',
                            quantity: '1',
                            amount: ''
                          });
                        }}
                      >
                        <Plus />
                        Add More Service
                      </Button>
                    </div>
                  </>
                )}
              </>
            )}
          </FormItem>
        )}
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 mt-6">
        <DirectSalePayment control={control} />
        <FormInput
          type="decimal"
          control={control}
          name="additionalFee"
          label="Additional Fee"
          placeholder="Additional Fee"
          displayMessage={false}
        />
        <FormInput
          type="decimal"
          control={control}
          name="transportFee"
          label="Transpontation Fee"
          placeholder="Transpontation Fee"
          displayMessage={false}
        />
        <FormInput
          type="decimal"
          control={control}
          name="serviceFee"
          label="Service Fee"
          placeholder="Service Fee"
          displayMessage={false}
        />

        <FormField
          control={control}
          name="paymentMethod"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Payment Method</FormLabel>
              <CustomSelect
                data={[{ label: 'ABA KHQR', value: 'abapay_khqr_deeplink' }]}
                value={field.value}
                onValueChange={field.onChange}
              />
              {/* <FormMessage /> */}
            </FormItem>
          )}
        />
        {/* <FormField
          control={control}
          name="paymentDate"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Payment Date</FormLabel>
              <DatePicker date={new Date(field.value)} onDateTimeChange={field.onChange} />
            </FormItem>
          )}
        /> */}
        {paymentStatusWatcher !== 'PAID' && (
          <FormField
            control={control}
            name="nextPaymentDate"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Next Payment Date</FormLabel>
                <DatePicker
                  date={field.value ? new Date(field.value) : undefined}
                  onDateTimeChange={field.onChange}
                />
                {/* <FormMessage /> */}
              </FormItem>
            )}
          />
        )}

        {!isEditWatcher && (
          <FormField
            control={control}
            name="file"
            render={({ field }) => (
              <FormItem>
                <Uploader
                  className="h-[62px]"
                  file={field.value}
                  onUploaded={(files) => {
                    field.onChange(files[0]);
                  }}
                >
                  {field.value && (
                    <img
                      className="h-[62px]"
                      src={URL.createObjectURL(field.value)}
                      alt="Payment Attachment"
                    />
                  )}
                </Uploader>
              </FormItem>
            )}
          />
        )}
        <FormField
          control={control}
          name="remark"
          render={({ field }) => (
            <FormItem className="col-span-3">
              <FormLabel>Remark</FormLabel>
              <Textarea placeholder="Remark" value={field.value} onChange={field.onChange} />
              {/* <FormMessage /> */}
            </FormItem>
          )}
        />
        {/* <DirectSaleDiscount control={control} /> */}
      </div>
    </div>
  );
}
