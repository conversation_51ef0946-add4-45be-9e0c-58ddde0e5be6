import { Button } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  Di<PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON>Header,
  DialogTitle
} from '@/components/ui/dialog';
import { useForm, useWatch } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { directSaleScheme, type DirectSaleProps } from '@/lib/schema/direct-sale-schema';
import { Form } from '../../ui/form';
import CustomTabs from '../custom-tabs';
import ServiceDetailsContent from '../service-details-content';
import { useState } from 'react';
import DirectSaleDetailContent from './direct-sale-detail-content';
import { useDirectSaleUsersAddressQuery } from '@/hooks/use-direct-sale-user-query';
import { useProductsByCategoryIdQuery } from '@/hooks/use-products-product-option-query';
import useCreateDirectSaleMutation from '@/hooks/use-create-direct-sale-mutation';
import { toast } from 'sonner';
import useDirectSaleEffect from '@/hooks/use-direct-sale-effect';

type Props = {
  orderDetail?: OrderListAttributes;
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
};

export default function DirectSaleDialog({ orderDetail, isOpen, setIsOpen }: Props) {
  const [selectedItems, setSelectedItems] = useState<{ [id: string]: string[] }>({});
  // const { data: categoryData, refetch } = useCategoryNamesQuery();
  // if (orderDetail) console.log({ orderDetail });

  const [, setTabItem] = useState('details');
  const { mutateAsync: createDirectSale, isPending: isPendingCreateDirectSale } =
    useCreateDirectSaleMutation(orderDetail?.bulkOrderId);

  const form = useForm<DirectSaleProps>({
    mode: 'onSubmit',
    resolver: zodResolver(directSaleScheme),
    defaultValues: {
      vatNo: '',
      customerId: '',
      date: undefined,
      address: '',
      category: '',
      sale: '',
      services: {
        serviceId: '',
        serviceTypeId: '',
        quantity: '1',
        amount: ''
      },
      pairServices: [],
      // assignCleaner: [],
      // paymentDate: new Date(),
      paymentMethod: 'abapay_khqr_deeplink',
      paymentStatus: 'PENDING',
      // payment
      deposit: '0',
      additionalFee: 0,
      transportFee: 0,
      serviceFee: 0,
      totalPayableAmount: '',
      remark: '',
      reseller: 'none',
      isEdit: false
    }
  });
  const { control } = form;
  const categoryIdWatcher = useWatch({ control, name: 'category' });
  const { directSalePreviewData, isPendingDirectSalePreview } = useDirectSaleEffect({
    form,
    orderDetail
  });

  const totalPayableAmountWAtcher = useWatch({ control, name: 'totalPayableAmount' });
  const saleWatcher = useWatch({ control, name: 'sale' });

  const { data: productData } = useProductsByCategoryIdQuery(categoryIdWatcher);
  const { data } = useDirectSaleUsersAddressQuery({ userId: saleWatcher });

  const onSubmit = async (values: DirectSaleProps) => {
    const addressDetails = data?.find((item) => `${item.id}` === `${values.address}`);

    const directSaleNextPaymentDate = values.nextPaymentDate;
    const directSaleCategory = categoryIdWatcher;
    let directSaleProduct = values.services?.serviceId;
    let directSaleProductOptionV2 = values.services?.serviceTypeId;
    const resellerId = values.reseller === 'none' ? undefined : values.reseller;

    console.log({ values });
    // if (categoryIdWatcher !== 'OTHER') {
    //   // find category name
    //   directSaleCategory =
    //     categoryData?.find((item) => `${item.id}` === `${categoryIdWatcher}`)?.nameEn || '';

    //   directSaleProduct = directSaleProduct ? productObj[directSaleProduct][0].nameEn : '';
    //   directSaleProductOptionV2 = directSaleProductOptionV2
    //     ? productOptionObj[directSaleProductOptionV2][0].nameEn
    //     : '';
    // }
    const payload =
      categoryIdWatcher === 'OTHER'
        ? []
        : [
            {
              vatNo: values.vatNo || undefined,
              id: values.services?.id || undefined,
              categoryType: categoryIdWatcher,
              // userId: parseInt(values.customerId, 10),
              userId: values.customerId,
              saleId: parseInt(values.sale, 10),
              qty: values.services?.quantity ? parseInt(values.services.quantity, 10) : 0,
              amount: values.services?.amount ? parseFloat(values.services.amount) : 0,
              transportFee: values.transportFee,
              additionalFee: values.additionalFee ? values.additionalFee : 0,
              deposit: values.deposit ? values.deposit : 0,
              productOptionIdV2: values.services?.serviceTypeId,
              directSaleProduct,
              directSaleProductOptionV2,
              directSaleCategory,
              isPrimary: true,
              // customerId: values.customerId,
              // customerFirstName: values.customerName,
              // customerLastName: '',
              // customerPhone: values.phonenumber,
              paymentMethod: values.paymentMethod,
              note: values.remark,
              scheduleStartDate: values.date?.toISOString(),
              address: addressDetails?.address,
              addressId: values.address,
              status: 'PENDING',
              paymentStatus: values.paymentStatus, // "PENDING" | "PARTIALLY_PAID" | "PAID"
              resellerId,
              serviceFee: values.serviceFee,
              directSaleNextPaymentDate
            }
          ];

    (values.pairServices || []).map((item, index) => {
      directSaleProduct = item.serviceId;
      directSaleProductOptionV2 = item.serviceTypeId;
      let isPrimary = false;

      if (categoryIdWatcher === 'OTHER' && index === 0) {
        isPrimary = true;
      }

      payload.push({
        vatNo: values.vatNo || undefined,
        id: item?.id || undefined,
        categoryType: categoryIdWatcher,
        userId: values.customerId,
        saleId: parseInt(values.sale, 10),
        qty: item.quantity ? parseInt(item.quantity, 10) : 0,
        amount: item.amount ? parseFloat(item.amount) : 0,
        productOptionIdV2: item.serviceTypeId,
        transportFee: 0,
        additionalFee: 0,
        deposit: 0,
        directSaleCategory,
        directSaleProduct,
        directSaleProductOptionV2,
        isPrimary,

        // customerFirstName: values.customerName,
        // customerPhone: values.phonenumber,
        paymentMethod: values.paymentMethod,
        note: values.remark,
        scheduleStartDate: values.date?.toISOString(),
        address: addressDetails?.address,
        addressId: values.address,
        status: 'PENDING',
        paymentStatus: values.paymentStatus, // "PENDING" | "PARTIALLY_PAID" | "PAID"
        resellerId,
        serviceFee: values.serviceFee,
        directSaleNextPaymentDate
      });
    });

    const formData = new FormData();
    formData.append('orders', JSON.stringify(payload));
    if (values.file) formData.append('imgUrl', values.file);
    console.log({ payload });
    await createDirectSale(formData);
    form.reset();
    toast.success('Created successfully');
    setIsOpen(false);
  };

  return (
    <Dialog
      key={isOpen ? orderDetail?.id || 'new' : 'closed'}
      open={isOpen}
      onOpenChange={setIsOpen}
    >
      <Form {...form}>
        <form
          key={orderDetail?.id || 'new'} // force RHF re-register
          id="direct-sale-form"
          onSubmit={form.handleSubmit(onSubmit, (error) => console.log('Error:', error))}
        >
          {/* <DialogTrigger asChild>
            {children || (
              <Button
                type="button"
                variant="default"
                className="h-[36px] w-[36px]  text-white  focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1">
                <Plus className="h-4 w-4" />
              </Button>
            )}
          </DialogTrigger> */}
          <DialogContent
            key={String(isOpen)}
            className="w-full max-w-[1000px] sm:max-w-[1000px] h-[90%] flex flex-col p-0"
          >
            <DialogHeader className="border-b p-6">
              <DialogTitle>Service</DialogTitle>
            </DialogHeader>
            <CustomTabs
              onChange={setTabItem}
              className="ml-6"
              tabs={[
                { label: 'Details', value: 'details' }
                // { label: 'Service Details', value: 'service-details' }
              ]}
            >
              {(tab) => {
                if (tab.value === 'details') {
                  return (
                    <DirectSaleDetailContent
                      data={directSalePreviewData?.pairProducts || []}
                      productData={productData || []}
                      control={control}
                      isLoading={isPendingDirectSalePreview}
                    />
                  );
                }
                return (
                  <div className="flex flex-1 flex-col overflow-y-auto gap-6 px-2">
                    <ServiceDetailsContent
                      title={<DialogTitle className="mb-8">Service Details</DialogTitle>}
                      className="p-0"
                      selectedServices={selectedItems}
                      setSelectedServices={setSelectedItems}
                    />
                    {/* {categoryWatcher && (
                      <>
                        <DialogTitle>Service Add-On</DialogTitle>
                        <ServiceServiceTypeSelectionData
                          control={control}
                          fields={fields}
                          onRemove={remove}
                          data={directSalePreviewData?.pairProducts}
                          isLoading={isPendingDirectSalePreview}
                        />
                      </>
                    )} */}
                  </div>
                );
              }}
            </CustomTabs>

            <DialogFooter className="p-6">
              <div className="flex flex-col flex-1">
                {/* {tabItem === 'service-details' && (
                  <div
                    className="text-primary hover:underline cursor-pointer self-end flex gap-2 items-center text-sm font-bold mb-6"
                    onClick={handleAddServiceAddOn}>
                    <Plus size="18" /> Add service Add On
                  </div>
                )} */}
                <div className="pb-4 flex gap-4 items-center self-end">
                  <div>Total Amount:</div>
                  <div className="font-bold min-w-[80px] text-right">
                    {totalPayableAmountWAtcher ? `$ ${totalPayableAmountWAtcher}` : '$ 0.00'}
                  </div>
                </div>
                <div className="flex flex-1 justify-end border-t pt-6">
                  {/* <FormField
                    control={control}
                    name="markAsPrivate"
                    render={({ field }) => (
                      <FormItem>
                        <div className="items-center gap-3 flex">
                          <Checkbox
                            id="markAsPrivate"
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                          <Label htmlFor="markAsPrivate">Mark As Private</Label>
                        </div>
                      </FormItem>
                    )}
                  /> */}

                  <Button
                    isLoading={isPendingCreateDirectSale}
                    form="direct-sale-form"
                    type="submit"
                  >
                    {orderDetail ? 'Update' : 'Create'}
                  </Button>
                </div>
              </div>
            </DialogFooter>
          </DialogContent>
        </form>
      </Form>
    </Dialog>
  );
}
