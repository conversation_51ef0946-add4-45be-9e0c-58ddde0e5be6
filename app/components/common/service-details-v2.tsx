import React from 'react';
import { Card } from '@/components/ui/card';

type Props = {
  service: ServicesProps;
  index: number;
};

const ServiceDetailsV2: React.FC<Props> = ({ service, index }) => {
  return (
    <Card className="w-auto p-6">
      <div className="flex items-center justify-between">
        <h2 className="text-base font-bold text-gray-700">Service {index + 1}</h2>
      </div>
      <div className="flex items-center justify-between">
        <div className="flex items-start gap-4  w-full">
          <img
            src={service.thumbnailUrl}
            alt={service.thumbnailUrl}
            className="w-[48px] h-[48px] rounded-full"
          />
          <div className="flex flex-col flex-1">
            <span className="font-bold text-base">
              {service.categoryNameEn}{' '}
              {service.addOns.length > 0 && <span>({service.addOns.length} Service Add-On)</span>}
            </span>
            <span className="text-sm text-muted-foreground">{service.productOptionEn}</span>
            {service.addOns.map((addOn, index) => (
              <span key={index} className="text-sm text-muted-foreground">
                {addOn.nameEn} x {addOn.qty}
              </span>
            ))}
          </div>
          <span className="font-bold">{service.amount}$</span>
        </div>
      </div>
    </Card>
  );
};

export default ServiceDetailsV2;
