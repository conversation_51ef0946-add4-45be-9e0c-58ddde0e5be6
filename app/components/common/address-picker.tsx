import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, use<PERSON>s<PERSON><PERSON><PERSON>oader } from '@react-google-maps/api';
import { useCallback, useRef, useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '../ui/dialog';
import { Button } from '../ui/button';
import { Command, CommandInput, CommandItem, CommandList } from '../ui/command';
import type { DirectSaleProps } from '@/lib/schema/direct-sale-schema';
import { FormField, FormItem, FormLabel } from '../ui/form';
import CustomSelect from './custom-select';
import {
  useAddDirectSaleUsersAddressMutation,
  useDirectSaleUsersAddressQuery
} from '@/hooks/use-direct-sale-user-query';
import { useWatch, type Control } from 'react-hook-form';
import { Input } from '../ui/input';
import clsx from 'clsx';
import { toast } from 'sonner';

const initialCenter = { lat: 11.5564, lng: 104.9282 }; // Phnom Penh
const containerStyle = {
  width: '100%',
  height: '400px',
  borderRadius: 8
};
interface Suggestion {
  description: string;
  place_id: string;
}

export default function AddressPicker({ control }: { control: Control<DirectSaleProps> }) {
  const GOOGLE_MAP_LIBRARIES: 'places'[] = ['places'];
  const [addressDetail, setAddressDetail] = useState('');
  const [markerPosition, setMarkerPosition] = useState(initialCenter);
  const [address, setAddress] = useState('');
  const { isLoaded } = useJsApiLoader({
    googleMapsApiKey: import.meta.env.VITE_GOOGLE_MAPS_API_KEY!,
    libraries: GOOGLE_MAP_LIBRARIES
  });
  const [isOpen, setIsOpen] = useState(false);
  const [searchValue, setSearchValue] = useState('');
  const [suggestions, setSuggestions] = useState<Suggestion[]>([]);
  const inputRef = useRef<HTMLInputElement>(null);
  const mapRef = useRef<google.maps.Map | null>(null);
  const userWatcher = useWatch({ name: 'sale', control });
  const { mutate } = useAddDirectSaleUsersAddressMutation();
  const { data } = useDirectSaleUsersAddressQuery({ userId: userWatcher });

  const handleSave = () => {
    if (!address) {
      toast.error('Address is required');
      return;
    }

    const payload = {
      address,
      latitude: `${markerPosition.lat}`,
      longitude: `${markerPosition.lng}`,
      addressDetail,
      userId: userWatcher
    };
    mutate(payload);
    // onSaved(payload);
    setIsOpen(false);
  };

  // Drag marker
  const handleDragEnd = useCallback((event: google.maps.MapMouseEvent) => {
    if (!event.latLng) return;

    const lat = event.latLng.lat();
    const lng = event.latLng.lng();
    setMarkerPosition({ lat, lng });

    const geocoder = new google.maps.Geocoder();
    geocoder.geocode({ location: { lat, lng } }, (results, status) => {
      if (status === 'OK' && results && results[0]) {
        setAddress(results[0].formatted_address);
        // setSearchValue(results[0].formatted_address);
      } else {
        setAddress('Address not found');
      }
    });
  }, []);
  // Search suggestions
  const handleSearchChange = useCallback((value: string) => {
    setSearchValue(value);
    if (!value) return setSuggestions([]);

    const service = new google.maps.places.AutocompleteService();
    service.getPlacePredictions(
      { input: value, componentRestrictions: { country: 'KH' } },
      (predictions, status) => {
        if (status === google.maps.places.PlacesServiceStatus.OK && predictions) {
          setSuggestions(
            predictions.map((p) => ({ description: p.description, place_id: p.place_id }))
          );
        } else {
          setSuggestions([]);
        }
      }
    );
  }, []);

  // Select suggestion
  const handleSelectSuggestion = useCallback((placeId: string) => {
    const geocoder = new google.maps.Geocoder();
    geocoder.geocode(
      {
        placeId
      },
      (results, status) => {
        if (status === 'OK' && results && results[0]) {
          const location = results[0].geometry.location;
          const lat = location.lat();
          const lng = location.lng();

          console.log({ location, lat, lng });
          setMarkerPosition({ lat, lng });
          setAddress(results[0].formatted_address);
          if (mapRef.current) mapRef.current.panTo({ lat, lng });
          setSuggestions([]);
          if (inputRef.current) inputRef.current.value = results[0].formatted_address;
        }
      }
    );
  }, []);

  return (
    <div className="flex flex-1 flex-col overflow-hidden">
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        {/* this w-full not work why? */}
        <DialogTrigger asChild>
          <div className="flex mb-2 justify-between">
            <FormLabel>Address</FormLabel>
            <FormLabel
              className={clsx('text-primary hover:underline cursor-pointer', {
                hidden: !userWatcher
              })}
            >
              Add
            </FormLabel>
          </div>
        </DialogTrigger>
        <DialogContent className="w-[670px]">
          <DialogHeader className="border-b pb-4">
            <DialogTitle>Add Location</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4">
            <div className="grid gap-3">
              {/* <Label htmlFor="name-1">Address</Label> */}
              <Command>
                <CommandInput
                  placeholder="Search address..."
                  ref={inputRef}
                  value={searchValue}
                  onValueChange={handleSearchChange}
                />
                {suggestions.length > 0 && (
                  <CommandList className="absolute z-999 bg-white top-[130px] border w-[350px]">
                    {suggestions.map((sug) => (
                      <CommandItem
                        key={sug.place_id}
                        onSelect={() => handleSelectSuggestion(sug.place_id)}
                      >
                        {sug.description}
                      </CommandItem>
                    ))}
                  </CommandList>
                )}
              </Command>
            </div>
          </div>
          {isLoaded ? (
            <div>
              <GoogleMap
                options={{
                  streetViewControl: false,
                  mapTypeControl: false,
                  fullscreenControl: false,
                  zoomControl: false,
                  rotateControl: false,
                  scaleControl: false,
                  clickableIcons: false,
                  gestureHandling: 'greedy' // allow drag & zoom
                }}
                mapContainerStyle={containerStyle}
                center={markerPosition}
                zoom={15}
              >
                <Marker position={markerPosition} draggable={true} onDragEnd={handleDragEnd} />
              </GoogleMap>
              <div className="space-y-2 mt-4">
                <p className="text-sm">Address</p>
                <Input disabled value={address} />
              </div>
              <div className="space-y-2 mt-4">
                <p className="text-sm">Name (optional)</p>
                <Input value={addressDetail} onChange={(e) => setAddressDetail(e.target.value)} />
              </div>
            </div>
          ) : (
            <p>Loading Map...</p>
          )}
          <DialogFooter className="mt-10">
            <Button size="sm" type="button" onClick={handleSave}>
              Save Address
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <FormField
        control={control}
        name="address"
        render={({ field }) => (
          <FormItem className="flex flex-1">
            <CustomSelect
              disabled={!userWatcher}
              data={(data || []).map((item) => ({
                label: item.addressDetail || item.address,
                value: item.id
              }))}
              value={`${field.value}`}
              onValueChange={(val) => field.onChange(`${val}`)}
            />
          </FormItem>
        )}
      />
    </div>
  );
}
