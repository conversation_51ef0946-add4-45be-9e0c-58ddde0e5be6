import type { ColumnDef } from '@tanstack/react-table';
import { Button } from '../ui/button';
import { MoreVertical, Pen, Trash2 } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '../ui/dropdown-menu';
import { Badge } from '../ui/badge';

export const bannerColumns: ColumnDef<BannerProps>[] = [
  {
    accessorKey: 'imageSrc',
    header: 'Image',
    cell: ({ row }) => {
      const image = row.getValue('imageSrc') as string;
      return <img src={image} alt="Banner" className="w-[160px] h-[90px] object-cover " />;
    }
  },

  {
    accessorKey: 'name',
    header: 'Name',
    cell: ({ row }) => <div>{row.getValue('name')}</div>
  },

  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => {
      const status = row.original.status;
      return <Badge variant={getStatusVariant(status)}>{status}</Badge>;
    }
  },

  {
    accessorKey: 'url',
    header: 'Url',
    cell: ({ row }) => <div>{row.getValue('url')}</div>
  },

  {
    id: 'actions',
    enableHiding: false,
    cell: () => {
      return (
        <div className="flex justify-end">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreVertical />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem className="text-blue-500" onClick={() => {}}>
                Edit
                <Pen className="text-blue-500" />
              </DropdownMenuItem>
              <DropdownMenuItem variant="destructive">
                Delete
                <Trash2 />
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      );
    }
  }
];

const getStatusVariant = (status: string) => {
  const lowercaseStatus = status.toLowerCase();
  if (lowercaseStatus === 'active') return 'approve';
  if (lowercaseStatus === 'inactive') return 'reject';
  if (lowercaseStatus === 'pending') return 'warning';
  return 'approve';
};
