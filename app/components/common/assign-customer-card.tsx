import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Input } from '../ui/input';
import { Button } from '../ui/button';
import { Plus } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '../ui/dialog';
import useCustomerQuery from '@/hooks/use-customer-query';
import { useState } from 'react';
import { Delete02Icon } from 'hugeicons-react';
import type { CustomerSchemaProps } from '@/lib/schema/coupon-schema';
import type { Control, FieldValues, Path } from 'react-hook-form';
import { FormField, FormItem, FormMessage } from '../ui/form';

type Props<T extends FieldValues> = {
  control?: Control<T>;
  name: Path<T>;
};

export default function AssignCustomersCard<T extends FieldValues>({ control, name }: Props<T>) {
  const [open, setOpen] = useState(false);
  const [searchText, setSearchText] = useState('');
  const { data, isLoading } = useCustomerQuery({
    currentPage: 0,
    columnFilters: [],
    searchText,
    pageSize: 100
  });

  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => {
        return (
          <FormItem>
            <Card className="flex w-full gap-4">
              <CardHeader>
                <CardTitle className="text-base">Specific Customers</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="rounded-lg overflow-hidden border border-gray-300">
                  <div className="flex bg-muted p-3 gap-4">
                    <div className="flex flex-1">
                      <span>Customers</span>
                    </div>
                    <div className="flex flex-1">
                      <span>Quantity</span>
                    </div>
                  </div>
                  {field.value.map((customer: CustomerSchemaProps, index: number) => (
                    <FormField
                      control={control}
                      key={index}
                      name={`customers.${index}.qty` as Path<T>}
                      render={({ field: qtyField }) => (
                        <FormItem className="flex px-4 py-3 gap-4 border-b border-gray-300">
                          <div className="flex flex-1 items-center">
                            <span>
                              {customer.firstName} {customer.lastName} - ({customer.username})
                            </span>
                          </div>
                          <div className="flex flex-1 items-center gap-4">
                            <Input
                              type="number"
                              value={`${qtyField.value}`}
                              onChange={(e) => {
                                qtyField.onChange(e.target.value);
                              }}
                            />
                            <Delete02Icon
                              size="22"
                              className="text-destructive cursor-pointer"
                              onClick={() => {
                                field.onChange([
                                  ...field.value.filter(
                                    (item: CustomerSchemaProps) => item.id !== customer.id
                                  )
                                ]);
                              }}
                            />
                          </div>

                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  ))}

                  <Dialog open={open} onOpenChange={setOpen}>
                    <DialogTrigger asChild>
                      <Button variant="link">
                        <Plus />
                        Assign Another Customer
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="w-[600px] gap-6 p-8">
                      <DialogHeader className="border-b pb-4">
                        <DialogTitle>Customer List</DialogTitle>
                      </DialogHeader>
                      <div className="flex flex-col h-[500px] gap-6">
                        <div className="grid gap-3">
                          <Input
                            value={searchText}
                            placeholder="Search Name and phone number"
                            onChange={(e) => setSearchText(e.target.value)}
                            id="name"
                            name="name"
                            defaultValue="Pedro Duarte"
                          />
                        </div>
                        <div className="overflow-y-auto space-y-4">
                          {isLoading ? (
                            <div>Loading...</div>
                          ) : (
                            data?.data.map((item) => (
                              <div
                                key={item.id}
                                className="flex gap-4 py-2 border-b px-4 cursor-pointer"
                                onClick={() => {
                                  field.onChange([
                                    ...field.value,
                                    { ...item, qty: 0, userId: item.id }
                                  ]);
                                  setOpen(false);
                                }}>
                                <div className="flex flex-1 items-center justify-between">
                                  <span>
                                    {item.firstName} {item.lastName}
                                  </span>
                                  <span>{item.username}</span>
                                </div>
                              </div>
                            ))
                          )}
                        </div>
                      </div>
                    </DialogContent>
                  </Dialog>
                </div>
              </CardContent>
            </Card>
          </FormItem>
        );
      }}
    />
  );
}
