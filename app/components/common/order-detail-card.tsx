import React from 'react';
import OrderAddress from '../common/order-address';
import OrderProfile from '../common/order-profile';
import { phoneNumber } from '@/lib/date-helper';
import { DateTimePicker } from './date-time-picker';
import { useScheduleMutation } from '@/hooks/use-schedule-mutation';

type OrderDetailCardProps = {
  data?: OrderListAttributes | null;
};

export default function OrderDetailCard({ data }: OrderDetailCardProps) {
  const { mutate } = useScheduleMutation(data?.bulkOrderId);
  const handleReschedule = (date: Date) => {
    mutate(date.toISOString());
  };

  if (!data) return null;

  return (
    <div className="p-6 rounded-xl shadow-sm bg-card">
      <p className="text-xl font-bold mb-6">Detail</p>

      <div className="flex items-start justify-between mb-4">
        <OrderProfile
          fullname={data.fullname}
          phone={data.phone ? phoneNumber(data.phone) : ''}
          profileUrl={data.profileUrl}
          paymentStatus={data.paymentStatus}
        />
      </div>

      <div className="flex justify-between text-sm items-center">
        <OrderAddress userAddress={data.UserAddress[0]} />
        <div className="flex gap-4 items-end">
          {data.paymentStatus !== 'PENDING' && data.status !== 'CANCELLED' && (
            <DateTimePicker label="Date" date={data.scheduleDate} onChange={handleReschedule} />
          )}
        </div>
      </div>
    </div>
  );
}
