import type { DirectSaleProps } from '@/lib/schema/direct-sale-schema';
import { type Control, type FieldArrayWithId } from 'react-hook-form';
import FormInput from './form-input';
import { Delete01Icon } from 'hugeicons-react';

type Props = {
  control: Control<DirectSaleProps>;
  fields: FieldArrayWithId<DirectSaleProps, 'pairServices', 'id'>[];
  onRemove: (index: number) => void;
};
export default function ServiceInputBoxSelectionData({ control, fields, onRemove }: Props) {
  // if (isLoading) return null;

  return fields.map((service, index) => (
    <div key={service.id} className="grid grid-cols-4 gap-6 col-span-4">
      <FormInput control={control} name={`pairServices.${index}.serviceId`} />
      <FormInput control={control} name={`pairServices.${index}.serviceTypeId`} />
      <FormInput control={control} name={`pairServices.${index}.quantity`} />
      <div className="flex items-center gap-4">
        <div className="w-full">
          <FormInput control={control} name={`pairServices.${index}.amount`} />
        </div>
        {index > 0 && (
          <button type="button" onClick={() => onRemove(index)} className="text-destructive">
            <Delete01Icon size="20" />
          </button>
        )}
      </div>
    </div>
  ));
}
