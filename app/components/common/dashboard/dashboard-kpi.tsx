import { motion, AnimatePresence } from 'framer-motion';
import { ArrowLeft01Icon, ArrowRight01Icon } from 'hugeicons-react';
import { useState } from 'react';
import clsx from 'clsx';
import { Skeleton } from '@/components/ui/skeleton';
import OverviewCard from '../overview-card';

type Props = {
  data: { [key: string]: TrendProps };
  isLoading?: boolean;
  kpiData: { label: string; value: string }[];
};

const ITEMS_PER_PAGE = 5;

export default function DashboardKpi(props: Props) {
  const [page, setPage] = useState(0); // page 0 = first 4, page 1 = next 4
  const { kpiData, data, isLoading } = props;
  const [direction, setDirection] = useState(0);
  const maxPage = Math.ceil(kpiData.length / ITEMS_PER_PAGE) - 1;

  const handleNext = () => {
    setDirection(1);
    setPage(1);
  };

  const handlePrevious = () => {
    setDirection(-1);
    setPage(0);
  };

  const slideVariants = {
    enter: (dir: number) => ({
      x: dir > 0 ? 600 : -600,
      opacity: 0,
      position: 'absolute'
    }),
    center: {
      x: 0,
      opacity: 1,
      position: 'static'
    },
    exit: (dir: number) => ({
      x: dir > 0 ? -600 : 600,
      opacity: 0,
      position: 'absolute'
    })
  };

  const visibleItems = kpiData.slice(page * ITEMS_PER_PAGE, page * ITEMS_PER_PAGE + ITEMS_PER_PAGE);

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {Array.from({ length: 5 }).map((_, index) => (
          <Skeleton key={index} className="h-30" />
        ))}
      </div>
    );
  }
  return (
    <div className="relative">
      {/* Animated Content */}
      <div className="overflow-hidden relative">
        <AnimatePresence initial={false} custom={direction}>
          <motion.div
            key={page}
            custom={direction}
            variants={slideVariants}
            initial="enter"
            animate="center"
            exit="exit"
            transition={{ duration: 0.3, ease: 'linear' }}
            className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-6"
          >
            {visibleItems.map((kpi, index) => {
              const trend = data[kpi.value] as TrendProps;
              return <OverviewCard key={index} {...trend} label={kpi.label} />;
            })}
          </motion.div>
        </AnimatePresence>
      </div>
      <div
        className={clsx(
          'absolute top-1/2 left-0 -translate-x-1/2 -translate-y-1/2 w-10 h-10 rounded-full bg-white shadow-sm flex items-center justify-center cursor-pointer',
          page === 0 && 'opacity-0 pointer-events-none'
        )}
        onClick={handlePrevious}
      >
        <ArrowLeft01Icon />
      </div>
      <div
        className={clsx(
          'absolute top-1/2 right-0 translate-x-1/2 -translate-y-1/2 w-10 h-10 rounded-full bg-white shadow-sm flex items-center justify-center cursor-pointer',
          page === maxPage && 'opacity-0 pointer-events-none'
        )}
        onClick={handleNext}
      >
        <ArrowRight01Icon />
      </div>
      {/* <ArrowLeft01Icon /> */}
    </div>
  );
}
