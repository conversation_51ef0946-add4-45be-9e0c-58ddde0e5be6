import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import TimePicker from './draggable/time-picker';
import { useState } from 'react';
import moment from 'moment';
import { formatDate } from '@/lib/date-helper';
import { Label } from '../ui/label';
import { Calendar03Icon } from 'hugeicons-react';

type Props = {
  date: string;
  onChange: (newDate: Date) => void;
  label?: string; // optional label override
  haveIcon?: boolean;
};

export function DateTimePicker({ label, date, onChange, haveIcon = false }: Props) {
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(
    date ? moment(date, 'DD-MM-YYYY').toDate() : undefined
  );
  const [selectedTime, setSelectedTime] = useState<string | undefined>(
    date ? moment(date).format('HH:mm') : undefined
  );
  const [open, setOpen] = useState(false);
  // console.log({ selectedTime });
  return (
    <div className="flex relative gap-3">
      <Popover open={open} onOpenChange={setOpen}>
        <div className="flex flex-col gap-2 w-full">
          {label && <Label className="text-muted-foreground">{label}</Label>}
          <PopoverTrigger asChild>
            <Button className="flex justify-between h-10" variant="outline">
              {date ? formatDate(date, true) : 'Select date'}
              {haveIcon && <Calendar03Icon />}
            </Button>
          </PopoverTrigger>
        </div>
        <PopoverContent className="w-auto p-4" align="center" side="left">
          <div className="text-center font-bold">Reschedule Date</div>
          <Calendar
            mode="single"
            selected={selectedDate}
            onSelect={setSelectedDate}
            disabled={(date) => date < new Date('1900-01-01')}
          />
          <TimePicker time={selectedTime} onChange={setSelectedTime} />
          <Button
            size="sm"
            className="w-full mt-6"
            onClick={() => {
              if (selectedDate && selectedTime) {
                const newDate = new Date(selectedDate);
                const [hour, minute] = selectedTime.split(':');
                newDate.setHours(Number.parseInt(hour));
                newDate.setMinutes(Number.parseInt(minute));
                onChange(newDate);
                setOpen(false);
              }
            }}
          >
            Reschedule Now
          </Button>
        </PopoverContent>
      </Popover>
    </div>
  );
}
