import { Calendar } from 'lucide-react';
import { Button } from '../ui/button';
import { Calendar as CalendarComponent } from '../ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '../ui/popover';
import { format } from 'date-fns';
import clsx from 'clsx';
import { useState } from 'react';

export type DateRangePickerProps = {
  dateRange: { from?: Date; to?: Date };
  setDateRange: (range: { from?: Date; to?: Date }) => void;
  isCalendarOpen: boolean;
  setIsCalendarOpen: (open: boolean) => void;
  iconHidden?: boolean;
  className?: string;
  initialDateRange?: { from?: Date; to?: Date };
  align?: 'start' | 'center' | 'end';
  side?: 'top' | 'right' | 'bottom' | 'left';
};

export default function DateRangePickerV2({
  dateRange,
  setDateRange,
  isCalendarOpen,
  setIsCalendarOpen,
  iconHidden,
  className,
  initialDateRange,
  align = 'end',
  side
}: DateRangePickerProps) {
  const [selectedRange, setSelectedRange] = useState<{ from?: Date; to?: Date }>({ ...dateRange });
  const handleReset = () => {
    setSelectedRange(
      initialDateRange || {
        from: undefined,
        to: undefined
      }
    );
  };

  const handleApply = () => {
    setDateRange({ ...selectedRange });
    setIsCalendarOpen(false);
  };

  return (
    <Popover open={isCalendarOpen} onOpenChange={setIsCalendarOpen}>
      <PopoverTrigger asChild>
        <Button
          size="sm"
          variant="outline"
          className={clsx('gap-2 bg-transparent min-w-[140px] justify-start', className)}
        >
          <Calendar className={clsx('h-4 w-4', { hidden: iconHidden })} />
          {dateRange.from && dateRange.to
            ? `${format(dateRange.from, 'dd MMM, yyyy')} - ${format(dateRange.to, 'dd MMM, yyyy')}`
            : 'Select date range'}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align={align} side={side}>
        <div className="flex gap-4 p-4">
          <div className="space-y-4">
            <div className="text-sm font-bold">From</div>
            <CalendarComponent
              mode="single"
              selected={selectedRange.from}
              onSelect={(date) => {
                setSelectedRange((prev) => ({
                  ...prev,
                  from: date,
                  // If "to" is before the new "from", reset it
                  to: prev.to && date && prev.to < date ? undefined : prev.to
                }));
              }}
              numberOfMonths={1}
              className="rounded-md border"
            />
          </div>
          <div className="space-y-4">
            <div className="text-sm font-bold">To</div>
            <CalendarComponent
              mode="single"
              selected={selectedRange.to}
              onSelect={(date) => {
                setSelectedRange((prev) => ({
                  ...prev,
                  to: date
                }));
              }}
              numberOfMonths={1}
              className="rounded-md border"
              disabled={selectedRange.from ? { before: selectedRange.from } : undefined}
              fromDate={selectedRange.from} // also prevents earlier selection
            />
          </div>
        </div>
        <div className="p-4 pt-0 flex items-end justify-end gap-4">
          <Button onClick={handleReset} size="sm" variant="secondary">
            Reset
          </Button>
          <Button onClick={handleApply} size="sm">
            Apply
          </Button>
        </div>
      </PopoverContent>
    </Popover>
  );
}
