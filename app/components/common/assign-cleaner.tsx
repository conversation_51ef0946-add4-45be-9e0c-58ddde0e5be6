import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar';
import { Check, Plus } from 'lucide-react';
import { Cancel01Icon } from 'hugeicons-react';
import SearchBar from './search-bar';
import { useEffect, useMemo, useState } from 'react';
import { getAvatarFallbackText } from '@/lib/utils';
import useCleanersQuery from '@/hooks/use-cleaners-query';

export default function AssignCleaner({
  orderCleaners,
  onChange
}: {
  orderCleaners?: CleanerAttributes[];
  onChange: (cleaner: CleanerAttributes) => void;
}) {
  const { data, isPending } = useCleanersQuery(1);
  const [cleaners, setCleaners] = useState<CleanerAttributes[]>([]);
  const [search, setSearch] = useState('');
  const [open, setOpen] = useState(false); // 👈 control popover state

  const cleaner = useMemo(() => {
    if (!data || data.length === 0) return [];
    return data.filter((item) => item.name.toLowerCase().includes(search.toLowerCase()));
  }, [search, data]);

  useEffect(() => {
    if (!orderCleaners) return;
    setCleaners([...orderCleaners]);
  }, [orderCleaners]);

  return (
    <div className="flex gap-3 overflow-x-scroll">
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button variant="ghost" className="text-primary bg-[#E8F0F7] rounded-full size-10">
            <Plus />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-90 max-h-[400px] p-0 overflow-hidden">
          <div className="flex flex-col h-[400px]">
            <div className="gap-y-2 flex justify-between p-4">
              <h4 className="leading-none font-medium">
                Assign Cleaners {cleaners.length > 0 ? `(${cleaners.length})` : ''}
              </h4>
              <Button
                variant="ghost"
                className="text-destructive py-0 h-auto"
                onClick={() => setOpen(false)}
              >
                Cancel
              </Button>
            </div>

            <div className="px-4">
              <SearchBar placeholder="Find Cleaner" onChange={setSearch} value={search} />
            </div>
            <div
              className="flex-1 overflow-y-auto p-4 space-y-4"
              onWheel={(e) => {
                e.stopPropagation();
              }}
            >
              {isPending ? (
                <div>loading..</div>
              ) : (
                cleaner.map((item) => {
                  const isActive = cleaners.some((cleaner) => cleaner.id === item.id);
                  return (
                    <CleanerItem
                      onClick={() => {
                        if (isActive) {
                          setCleaners(cleaners.filter((cleaner) => cleaner.id !== item.id));
                        } else {
                          setCleaners([...cleaners, item]);
                        }
                        onChange(item);
                      }}
                      isActive={isActive}
                      key={item.id}
                      item={item}
                    />
                  );
                })
              )}
            </div>
          </div>
        </PopoverContent>
      </Popover>
      {cleaners.map((cleaner) => (
        <CleanerAvatar
          item={cleaner}
          key={cleaner.id}
          onRemove={() => {
            setCleaners(cleaners.filter((item) => item !== cleaner));
            onChange(cleaner);
          }}
        />
      ))}
    </div>
  );
}

type ItemProps = {
  onRemove: () => void;
  item: CleanerAttributes;
};

const CleanerAvatar = ({ onRemove, item }: ItemProps) => {
  return (
    <div className="relative">
      <Avatar className="size-10">
        <AvatarImage src={item.image} />
        <AvatarFallback>{getAvatarFallbackText(item.name)}</AvatarFallback>
      </Avatar>
      <div
        className="absolute top-0 right-0 bg-white rounded-full p-[1px] shadow-2xs cursor-pointer"
        onClick={onRemove}
      >
        <Cancel01Icon className="size-3 text-red-500" />
      </div>
    </div>
  );
};

const CleanerItem = ({
  item,
  isActive,
  onClick
}: {
  item: CleanerAttributes;
  isActive: boolean;
  onClick: () => void;
}) => {
  return (
    <div className="flex gap-4 items-center overflow-auto cursor-pointer" onClick={onClick}>
      <Avatar className="size-10">
        <AvatarImage src={item?.image} />
        <AvatarFallback>{getAvatarFallbackText(item?.name)}</AvatarFallback>
      </Avatar>
      <div className="flex flex-1 overflow-auto">
        <span>{item?.name}</span>
      </div>
      {isActive && <Check className="size-4 text-primary" />}
    </div>
  );
};
