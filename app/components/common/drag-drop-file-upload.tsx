import { X } from 'lucide-react';
import { useState } from 'react';
import { Uploader } from './uploader';

type Props = {
  placeholder?: string;
};

export default function DragDropFileUpload({ placeholder }: Props) {
  const [imgs, setImgs] = useState<string[]>([]);

  console.log({ imgs });

  return (
    <div className="flex flex-col gap-2">
      <span>{placeholder}</span>
      {imgs.length > 0 && (
        <div className="grid grid-cols-5 gap-4 mb-8">
          {imgs.map((src, index) => (
            <Image
              key={index}
              src={src}
              onRemove={() => {
                setImgs(imgs.filter((_, i) => i !== index));
              }}
            />
          ))}
        </div>
      )}
      <Uploader onUploaded={(urls) => setImgs([...imgs, ...urls])} />
    </div>
  );
}

const Image = ({ src, onRemove }: { src: string; onRemove: () => void }) => {
  return (
    <div className="relative bg-gray-100 rounded-lg overflow-hidden shadow-sm aspect-square">
      <img src={src} alt={src} className="object-cover w-full h-full rounded-sm" />
      <div
        className="absolute top-2 right-2 cursor-pointer z-10 bg-white/30 rounded-full size-8 items-center flex justify-center"
        onClick={onRemove}
      >
        <X className="text-destructive" />
      </div>
    </div>
  );
};
