import React from 'react';
import PaymentLabel from './order-payment-label';
import { Card } from '../ui/card';
import AssignCleaner from './assign-cleaner';
// import { Textarea } from '../ui/textarea';

type Props = {
  serviceFeeDisplay: string;
  discountDisplay: string;
  transportFeeDisplay: string;
  subTotalDisplay: string;
  vatFeeDisplay: string;
  paymentMethodDisplay: string;
  totalPayableAmountDisplay: string;
  couponCode: string;
  totalAmountDisplay: string;
  bulkOrderId: string;
  orderCleaners: CleanerAttributes[];
};

const PaymentInfo: React.FC<Props> = (props) => {
  return (
    <Card className="w-[340px] mx-auto bg-card p-6 gap-2">
      <AssignCleaner orderCleaners={props.orderCleaners} bulkOrderId={props.bulkOrderId} />

      <div className="relative space-y-4">
        <h2 className="text-base font-bold text-gray-700">Payment Information</h2>
        <div className="w-full bg-gray-100 h-[2px]" />
        <div className="space-y-4">
          {/* Service */}
          <PaymentLabel label="Total Amount:" value={props.totalAmountDisplay} />
          {props.discountDisplay && (
            <PaymentLabel label="Discount:" value={`(${props.discountDisplay})`} />
          )}
          {props.couponCode && <PaymentLabel label="Coupon:" value={props.couponCode} />}
          <PaymentLabel label="Service Fee:" value={props.serviceFeeDisplay} />
          <PaymentLabel label="Transport Fee:" value={props.transportFeeDisplay} />
          <PaymentLabel label="SubTotal:" value={props.subTotalDisplay} />
          <PaymentLabel label="VAT (10%):" value={props.vatFeeDisplay} />
          <PaymentLabel label="Payment Method:" value={props.paymentMethodDisplay} />
          <div className="w-full bg-gray-100 h-[2px]" />
          <span className="font-bold text-gray-900">
            <PaymentLabel label="Total:" value={props.totalPayableAmountDisplay ?? 0} />
          </span>
        </div>
      </div>
    </Card>
  );
};

export default PaymentInfo;
