import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Clock } from 'lucide-react';
type Props = {
  time?: string;
  onChange?: (time: string) => void;
};
export default function TimePicker({ time, onChange }: Props) {
  const timeOptions = [
    '09:00',
    '09:30',
    '10:00',
    '10:30',
    '11:00',
    '11:30',
    '12:00',
    '12:30',
    '13:00',
    '13:30',
    '14:00',
    '14:30',
    '15:00',
    '15:30',
    '16:00',
    '16:30',
    '17:00',
    '17:30',
    '18:00'
  ];

  return (
    <div className="flex items-center gap-2">
      <Select value={time} onValueChange={onChange}>
        <SelectTrigger className="w-full [&>*:last-child]:hidden">
          <SelectValue />

          <Clock className="mr-2 h-4 w-4" />
        </SelectTrigger>
        <SelectContent>
          {timeOptions.map((timeOption) => (
            <SelectItem key={timeOption} value={timeOption}>
              {timeOption}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}
