import React from 'react';
import { Badge } from '../ui/badge';
import { getBadgeStatusVariant, getStatusDisplayText } from '@/lib/utils';
import { Button } from '../ui/button';
import clsx from 'clsx';
import { PrinterIcon } from 'lucide-react';
import usePrintMutation from '@/hooks/use-print-mutation';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger
} from '../ui/alert-dialog';
import useCancelOrderMutation from '@/hooks/use-cancel-order-mutation';

type Props = {
  orderId: string;
  status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'ACCEPTED' | 'CANCELLED';
  paymentStatus?: 'PENDING' | 'PAID' | 'UNPAID' | 'IN-REVIEW';
  onClick?: (status: string) => void;
  isPending: boolean;
  paymentMethodDisplay: string;
};

const OrderHeaderDetail: React.FC<Props> = ({
  onClick,
  status,
  orderId,
  isPending,
  // paymentMethodDisplay,
  paymentStatus
}) => {
  const { mutateAsync: print } = usePrintMutation();
  const { mutateAsync: cancelOrder } = useCancelOrderMutation();
  const buttonConfig: {
    [key: string]: { text: string; variant: 'default' | 'destructive' | 'outline' | 'success' };
  } = {
    PENDING: {
      text: 'Confirm Order',
      variant: 'default'
    },
    ACCEPTED: {
      text: 'Completed Order',
      variant: 'success'
    },
    IN_PROGRESS: {
      text: 'Complete Order',
      variant: 'success'
    }
    // COMPLETED: {
    //   text: 'Order Completed',
    //   variant: 'outline'
    // },
    // CANCELLED: {
    //   text: 'Order Cancelled',
    //   variant: 'destructive'
    // }
  };

  const handlePrintClick = () => {
    print(orderId);
  };

  const handleCancel = () => {
    cancelOrder(orderId);
  };

  return (
    <div className="h-[88px] w-full items-center flex px-6 bg-background">
      <div className="flex space-x-4 items-center">
        <h2 className="text-base font-semibold">
          Order ID <span className="font-bold">#{orderId}</span>
        </h2>
        <div className="h-5 w-px bg-gray-300" />
        <Badge variant={getBadgeStatusVariant(status)} className="rounded-full h-8 px-4 text-sm">
          {getStatusDisplayText(status)}
        </Badge>
      </div>

      <div className="flex items-center gap-3 ml-auto">
        <Button
          type="button"
          isLoading={isPending}
          variant="outline"
          onClick={handlePrintClick}
          className={clsx('h-[36px]', {
            hidden: !onClick
          })}
        >
          Print
          <PrinterIcon />
        </Button>
        {status !== 'CANCELLED' && paymentStatus !== 'PENDING' && (
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button size="sm" variant="destructive">
                Cancel
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Confirmation</AlertDialogTitle>
                <AlertDialogDescription>
                  Are you sure you want to cancel this order?
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel className="h-[36px]">No</AlertDialogCancel>
                <AlertDialogAction onClick={handleCancel} className="h-[36px] bg-destructive">
                  Yes
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        )}

        {status === 'PENDING' || status === 'ACCEPTED' || status === 'IN_PROGRESS' ? (
          <Button
            type="button"
            isLoading={isPending}
            variant={buttonConfig[status].variant}
            onClick={() => onClick && onClick(status)}
            size="sm"
            className={clsx({
              hidden: !onClick
            })}
          >
            {buttonConfig[status].text}
          </Button>
        ) : null}
      </div>
    </div>
  );
};

export default OrderHeaderDetail;
