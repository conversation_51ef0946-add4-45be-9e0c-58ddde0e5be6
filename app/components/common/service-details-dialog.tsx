import { Button } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON>Footer,
  <PERSON><PERSON>Header,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog';
import { Card, CardContent } from '../ui/card';
import { useEffect, useMemo, useState } from 'react';
import useServiceItemsQuery from '@/hooks/use-service-item-query';
import { Checkbox } from '../ui/checkbox';
import { Label } from '../ui/label';
import { Delete01Icon, MessageAdd01Icon } from 'hugeicons-react';
import { Popover, PopoverContent, PopoverTrigger } from '../ui/popover';
import { Textarea } from '../ui/textarea';
import { groupBy } from 'lodash';
import clsx from 'clsx';
import { useAddServiceItemDetailsMutation } from '@/hooks/use-add-service-item-details-mutation';

export function ServiceDetailsDialog({
  bulkOrderId,
  serviceItemDetails
}: {
  bulkOrderId: string;
  serviceItemDetails?: ServiceItemDetailProps[];
}) {
  const [isOpen, setIsOpen] = useState(false);
  const { data, isPending } = useServiceItemsQuery('1');
  const { mutateAsync } = useAddServiceItemDetailsMutation();
  // const [confirmSelectedItem, setConfirmSelectedItem] = useState<{ [id: string]: string[] }>({});
  const [selectedItems, setSelectedItems] = useState<{ [id: string]: string[] }>({});
  const serviceObject = useMemo(() => {
    return groupBy(data, 'id');
  }, [data]);

  const handleAddService = () => setIsOpen(true);

  useEffect(() => {
    if (serviceItemDetails && isOpen) {
      const temp = serviceItemDetails.reduce(
        (acc, item) => {
          acc[item.serviceItemId] = [...item.serviceItemRemarks];
          return acc;
        },
        {} as { [id: string]: string[] }
      );
      setSelectedItems({ ...temp });
    }
  }, [serviceItemDetails, isOpen]);

  const handleSaveClick = async () => {
    const keys = Object.keys(selectedItems);
    const payload: {
      bulkOrderId: string;
      serviceItems: { serviceItemId: string; serviceItemRemarks: string[] }[];
    } = {
      bulkOrderId,
      serviceItems: keys.map((id) => ({
        serviceItemId: id,
        serviceItemRemarks: selectedItems[id]
      }))
    };

    console.log({ payload });
    await mutateAsync(payload);
    setIsOpen(false);
  };

  return (
    <Card>
      <CardContent className="flex flex-col gap-4">
        <h2 className="text-base font-bold text-gray-700">Service Details</h2>
        {serviceItemDetails?.map((item, index) => {
          return (
            <div key={index} className="flex flex-col">
              <div className="flex items-center gap-3">
                <Checkbox id={item.serviceItemId} checked />
                <Label className="flex flex-1" htmlFor={item.serviceItemId}>
                  {serviceObject?.[item.serviceItemId] &&
                  serviceObject?.[item.serviceItemId].length > 0
                    ? serviceObject?.[item.serviceItemId][0].name
                    : ''}
                </Label>
                <Label>x{item.serviceItemRemarks.length}</Label>
              </div>
            </div>
          );
        })}
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
          <DialogTrigger asChild>
            <div className="justify-end flex">
              <div
                className="text-primary text-sm font-bold cursor-pointer"
                onClick={handleAddService}
              >
                Add Service Detail
              </div>
            </div>
          </DialogTrigger>
          <DialogContent className="w-[80vw] h-[80vh] flex flex-col p-0">
            <DialogHeader className="justify-between border-b p-4">
              <DialogTitle>Service</DialogTitle>
            </DialogHeader>
            {/* not working why? */}
            <div className="flex-1 relative overflow-y-auto p-6 pt-2">
              <h4 className="mb-6">Service Details</h4>
              {isPending ? (
                <div>loading</div>
              ) : (
                data?.map((item, index) => (
                  <ServiceContent
                    setSelectedItems={setSelectedItems}
                    onClick={() => {
                      console.log({ index, selectedItems });
                      if (selectedItems[item.id]) {
                        setSelectedItems((prev) => {
                          delete prev[item.id];
                          return { ...prev };
                        });
                      } else {
                        setSelectedItems((prev) => {
                          prev[item.id] = [''];
                          return { ...prev };
                        });
                      }
                    }}
                    selectedItems={selectedItems}
                    item={item}
                    key={item.id}
                  />
                ))
              )}
            </div>
            <DialogFooter className="border-t p-4">
              <Button size="sm" type="button" onClick={handleSaveClick}>
                Save
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  );
}

const ServiceContent = ({
  item,
  onClick,
  selectedItems,
  setSelectedItems
}: {
  item: ServiceItemAttributes;
  onClick: () => void;
  selectedItems: { [id: string]: string[] };
  setSelectedItems: React.Dispatch<React.SetStateAction<{ [id: string]: string[] }>>;
}) => {
  return (
    <div className="flex flex-col">
      <div className="flex items-center gap-3">
        <Checkbox id={item.id} checked={item.id in selectedItems} onCheckedChange={onClick} />
        <Label className="flex flex-1" htmlFor={item.id}>
          {item.name}
        </Label>
        <div className="flex items-end justify-end h-4">
          {item.id in selectedItems && (
            <div
              className="h-full pl-4 text-sm cursor-pointer text-primary font-semibold"
              onClick={() => {
                selectedItems[item.id].push('');
                setSelectedItems({ ...selectedItems });
              }}
            >
              Add More Service
            </div>
          )}
        </div>
      </div>

      {/* {item.id in selectedItems && (
        <div className="flex">
          <Label className="w-[120px]">Quantity</Label>
          <div className="w-10">
            <Input
              className="text-center"
              value={selectedItems[item.id]?.length}
              maxLength={2}
              onChange={(e) => {
                setSelectedItems((prev) => {
                  prev[item.id] = Array.from({ length: Number(e.target.value) }, () => '');
                  return { ...prev };
                });
              }}
            />
          </div>
        </div>
      )} */}
      <div className="relative space-y-4 my-4">
        {selectedItems[item.id]?.map((text, index) => (
          <div key={index} className="flex flex-1 w-full justify-center my-4">
            <div className="w-[120px]">
              <span className="text-sm">
                {item.name} {index + 1}
              </span>
            </div>
            <div className="flex flex-1 border-b pb-4">
              <div>{item.description}</div>
            </div>
            <div className="flex items-start gap-4">
              <Popover>
                <PopoverTrigger asChild>
                  <div className="flex items-center cursor-pointer relative">
                    <MessageAdd01Icon className="size-5 text-primary ml-4" />
                    {text.length > 0 && (
                      <div className="absolute top-[1px] right-[1px] w-[7px] h-[7px] rounded-full bg-destructive" />
                    )}
                  </div>
                </PopoverTrigger>
                <PopoverContent side="bottom" align="end">
                  <Textarea
                    className="border-0 resize-none shadow-none"
                    value={text}
                    onChange={(e) => {
                      setSelectedItems((prev) => {
                        prev[item.id][index] = e.target.value;
                        return { ...prev };
                      });
                    }}
                  />
                </PopoverContent>
              </Popover>
              {/* this one not working? */}
              <Delete01Icon
                className={clsx('size-5 text-destructive cursor-pointer', {
                  hidden: index === 0 && selectedItems[item.id]?.length === 1
                })}
                onClick={() => {
                  selectedItems[item.id].splice(index, 1);
                  setSelectedItems({ ...selectedItems });
                }}
              />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
