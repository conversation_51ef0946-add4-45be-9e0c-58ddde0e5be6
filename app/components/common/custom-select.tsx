import clsx from 'clsx';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';

type Props = {
  data: { label: string; value: string }[];
  value: string;
  onValueChange: (value: string) => void;
  className?: string;
  placeholder?: string;
  prefix?: string;
};

export default function CustomSelect({
  data,
  value,
  onValueChange,
  className,
  placeholder,
  prefix
}: Props) {
  return (
    <Select value={value} onValueChange={onValueChange}>
      <SelectTrigger className={clsx('w-full', className)}>
        {prefix && <span className="text-sm">{prefix}</span>}
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        {data.map((item) => (
          <SelectItem key={item.value} value={item.value}>
            {item.label}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}
