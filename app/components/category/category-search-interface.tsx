import SearchBar from '../common/search-bar';
import { Combobox } from '../common/combobox';
import { useTranslation } from 'react-i18next';
import DateRangePickerV2, { type DateRangePickerProps } from '../common/date-range-picker-v2';
// import DirectSaleDialog from '../common/direct-sale-dialog';
// import DirectSaleDialog from '../common/direct-sale-dialog';

type Props = {
  statusFilter: string;
  setStatusFilter: (value: string) => void;
  paymentStatusFilter: string;
  setPaymentStatusFilter: (value: string) => void;
  searchValue: string;
  setSearchValue: (value: string) => void;
} & DateRangePickerProps;

export default function CategorySearchInterface({
  setStatusFilter,
  statusFilter,
  paymentStatusFilter,
  setPaymentStatusFilter,
  searchValue,
  setSearchValue,
  ...rest
}: Props) {
  const { t } = useTranslation();

  return (
    <div className="w-full pb-4 px-4 gap-3 flex flex-col">
      <div className="flex gap-3">
        <SearchBar
          placeholder="Search for order id..."
          value={searchValue}
          onChange={setSearchValue}
        />
        {/* <DirectSaleDialog /> */}
      </div>
      <div className="flex gap-3 w-full">
        <Combobox
          placeholder={t('allStatus')}
          data={[
            { label: 'All Status', value: 'all' },
            { label: 'Pending', value: 'Pending' },
            { label: 'Confirmed', value: 'Accepted' },
            { label: 'Completed', value: 'Completed' }
          ]}
          className="w-[120px]"
          value={statusFilter}
          onSelect={setStatusFilter}
        />
        <Combobox
          placeholder={t('paymentStatus')}
          data={[
            { label: 'All Status', value: 'all' },
            { label: 'Payment: Pending', value: 'Pending' },
            { label: 'Payment: Paid & In-Review', value: 'Paid' }
          ]}
          className="flex flex-1"
          value={paymentStatusFilter}
          onSelect={setPaymentStatusFilter}
        />
      </div>
      <div className="flex gap-3 w-full">
        <DateRangePickerV2 align="start" side="left" iconHidden className="flex flex-1" {...rest} />
      </div>
    </div>
  );
}
