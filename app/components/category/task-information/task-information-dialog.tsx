import CustomTabs from '@/components/common/custom-tabs';
import DraggableInputBoxPanel from '@/components/common/draggable/draggable-inputbox-panel';
import { Button } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON>Footer,
  <PERSON><PERSON>Header,
  DialogTitle
} from '@/components/ui/dialog';
import { useTranslation } from 'react-i18next';
import { useForm, type Path } from 'react-hook-form';
import {
  taskInformationSchema,
  type TaskInformationSchemaProps
} from '@/lib/schema/category-schema';
import FormInput from '@/components/common/form-input';
import { FormControl, FormField, FormItem, FormMessage, Form } from '@/components/ui/form';
import { zodResolver } from '@hookform/resolvers/zod';

const languages = [
  { label: 'English', value: 'en' },
  { label: 'Khmer', value: 'km' },
  { label: 'Vietnamese', value: 'vn' },
  { label: 'Chinese (Traditional)', value: 'zh' },
  { label: 'Chinese (Simplified)', value: 'cn' }
];

type Props = {
  title: string;
  onSave: (data: TaskInformationSchemaProps) => void;
  open: boolean;
  setOpen: (open: boolean) => void;
  value?: TaskInformationSchemaProps;
};

export function TaskInformationDialog({ title, onSave, open, setOpen, value }: Props) {
  const { t } = useTranslation();
  const form = useForm<TaskInformationSchemaProps>({
    mode: 'onSubmit',
    resolver: zodResolver(taskInformationSchema),
    defaultValues: value || {
      en: { title: '', description: [] },
      km: { title: '', description: [] },
      vi: { title: '', description: [] },
      tw: { title: '', description: [] },
      cn: { title: '', description: [] }
    }
  });

  // const { fields, remove } = fieldArray;
  // const index = fields.length - 1;

  const handleOpenChange = (open: boolean) => {
    // if (!open) {
    //   remove(index);
    // }
    setOpen(open);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSave)}>
        <Dialog open={open} onOpenChange={handleOpenChange}>
          <DialogContent
            className="p-0 w-[75vw] flex flex-col h-[70vh] overflow-hidden gap-0"
            onInteractOutside={(e) => e.preventDefault()}
          >
            <DialogHeader className="p-6  ">
              <DialogTitle>{title}</DialogTitle>
            </DialogHeader>
            <CustomTabs tabs={languages}>
              {(tab) => (
                <div>
                  <FormInput
                    placeholder={tab.label}
                    control={form.control}
                    name={`${tab.value}.title` as Path<TaskInformationSchemaProps>}
                  />
                  <FormField
                    control={form.control}
                    name={`${tab.value}.description` as Path<TaskInformationSchemaProps>}
                    render={() => (
                      <FormItem>
                        <FormControl>
                          <DraggableInputBoxPanel
                            buttonText={t('categoryPage.addProduct')}
                            control={form.control}
                            name={tab.value as Path<TaskInformationSchemaProps>}
                            label={tab.label}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              )}
            </CustomTabs>
            <DialogFooter className="border-t p-6">
              <Button
                onClick={form.handleSubmit(onSave, (e) => console.log({ e }))}
                type="submit"
                size="sm"
              >
                Save
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </form>
    </Form>
  );
}
