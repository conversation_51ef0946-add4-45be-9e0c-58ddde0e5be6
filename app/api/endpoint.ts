export const API_ENDPOINT = {
  LOGIN: 'auth/account/login',
  PROFILE: 'profile/info',
  LOGOUT: 'logout',

  ORDERS: 'order/list',
  ORDER_DETAIL: 'order',
  UPDATE_ORDER_STATUS: 'order/update',
  CATEGORIES: 'category/list',
  CATEGORIES_NAME: 'category/list-names',
  CATEGORY_REARRANGE: 'category/rearrange',
  EXPORT_EXCEL: 'fileExport',
  PRINT: 'fileExport/cleaner-client-tasks',
  TOPUP: 'topup',
  ANNOUNCEMENTS: 'announcement/list',
  CREATE_ANNOUNCEMENT: 'announcement/create',

  CLEANER_DETAIL: 'cleaner/',
  CLEANERS: 'cleaner/list',
  CRAETE_CLEANER: 'cleaner/create',
  UPDATE_CLEANER: 'cleaner/update/',

  ADD_CLEANER_DETAIL: 'cleaner/add-cleaner-detail',

  UPDATE_SERVICE_ITEM: 'service-item/update/',
  SERVICE_ITEM_DETAIL: 'service-item/item/',
  SERVICE_ITEMS: 'service-item/list/item',
  CREATE_SERVICE_ITEM: 'service-item/create/item',
  ADD_SERVICE_DETAILS: 'service-item/add-service-details',

  CUSTOMERS: 'customer/list',
  RESOUCE_REFERRAL: 'customer/resource-referral',

  UPDATE_CUSTOMER_SERVICE: 'customer-service/create',
  RESCHEDULE: 'order/reschedule/',
  CANCEL_ORDER: 'order/cancel/',

  CREATE_COUPON: 'coupon/create',
  UPDATE_COUPON: 'coupon/update',
  COUPONS: 'coupon/list',
  COUPON_DETAIL: 'coupon/detail/'
};

export type ApiEndpointProps = (typeof API_ENDPOINT)[keyof typeof API_ENDPOINT];
