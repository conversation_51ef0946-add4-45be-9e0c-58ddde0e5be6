export const API_ENDPOINT = {
  LOGIN: 'auth/account/login',
  PROFILE: 'profile/info',
  LOGOUT: 'logout',
  CHANGE_PASSWORD: 'profile/password/update',
  UPDATE_PROFILE: 'profile/info/update',

  ORDERS: 'order/list',
  CALENDAR_ORDERS: 'order/calendar/list',
  ORDER_DETAIL: 'order',
  UPDATE_ORDER_STATUS: 'order/update',
  CATEGORIES: 'category/list',
  CATEGORIES_NAME: 'category/list-names',
  PRODUCT_DETAIL_BY_CATEGORY_ID: 'category/{id}/product/list',
  CATEGORY_REARRANGE: 'category/rearrange',
  EXPORT_EXCEL: 'fileExport',
  PRINT: 'fileExport/cleaner-client-tasks',
  TOPUP: 'topup/list',
  ANNOUNCEMENTS: 'announcement/list',
  CREATE_ANNOUNCEMENT: 'announcement/create',

  CLEANER_DETAIL: 'cleaner/',
  CLEANERS: 'cleaner/list',
  CRAETE_CLEANER: 'cleaner/create',
  UPDATE_CLEANER: 'cleaner/update/',
  UPDATE_CLEANER_EXPERTISE: 'cleaner/update/expertise/',

  ADD_CLEANER_DETAIL: 'cleaner/add-cleaner-detail',

  UPDATE_SERVICE_ITEM: 'service-item/update/',
  SERVICE_ITEM_DETAIL: 'service-item/item/',
  SERVICE_ITEMS: 'service-item/list/item',
  CREATE_SERVICE_ITEM: 'service-item/create/item',
  ADD_SERVICE_DETAILS: 'service-item/add-service-details',

  CUSTOMERS: 'customer/list',
  CUSTOMER_TYPE_DIRECT_SALE: 'customer/direct-sale-users',
  RESOUCE_REFERRAL: 'customer/resource-referral',
  OTP: 'customer/otp',

  UPDATE_CUSTOMER_SERVICE: 'customer-service/create',
  RESCHEDULE: 'order/reschedule/',
  CANCEL_ORDER: 'order/cancel/',

  CREATE_COUPON: 'coupon/create',
  UPDATE_COUPON: 'coupon/update',
  COUPONS: 'coupon/list',
  COUPON_DETAIL: 'coupon/detail/',
  ADD_USER_TO_COUPON: 'coupon/add-user',
  REMOVE_USER_FROM_COUPON: 'coupon/remove-user',
  GET_SELECTED_PRODUCT_DETAIL: 'coupon/{id}/selected-product/list',

  BLOCKED_SCHEDULE: 'order-schedule/list',
  CREATE_BLOCKED_SCHEDULE: 'order-schedule/create',
  UPDATE_BLOCKED_SCHEDULE: 'order-schedule/update',
  BLOCKED_SCHEDULE_DETAIL: 'order-schedule/',

  CREATE_TOPUP: 'topup/add',
  CHECK_TOPUP_PAYMENT_STATUS: 'topup/check-payment-status',

  // ASSIGN COUPON
  PAYMENT_LINK: 'payment-link/list',
  ADD_PAYMENT_LINK: 'payment-link/add',
  CHECK_PAYMENT_STATUS: 'payment-link/check-payment-status',

  COUPON_LIST_SUMMARY: 'coupon/list/summary',
  PRODUCT_OPTION_BY_PRODUCT_ID: 'product/{id}/product-option',

  DIRECT_SALE_PREVIEW: 'direct-sale/preview',
  DIRECT_SALE_USERS: 'direct-sale/users',
  DIRECT_SALE_USERS_ADDRESS: 'direct-sale/users-address',
  ADD_DIRECT_SALE_USERS_ADDRESS: 'direct-sale/add-users-address',
  GET_DATA_DIRECT_SALE: 'direct-sale/get-data',
  CREATE_DIRECT_ORDER: 'direct-sale/create-order',
  UPDATE_DIRECT_SALE: 'direct-sale/edit-directsale/',

  PRINT_VAT_INVOICE: 'fileExport/vat-invoice',
  ABA_PAYMENT_STATUS: 'order/aba-payment-status',
  PRODUCT_DIRECT_SALE: 'direct-sale/list-product',
  EDIT_NOTE: 'direct-sale/edit-note/',
  LIST_DIRECT_SALE: 'direct-sale/list',

  MARK_AS_PAID: 'order/update/payment-status/',
  DELETE_RECEIPT: 'order/delete-receipt/',

  MARKETING_OVERVIEW: 'marketing/overview',
  REGISTER_CUSTOMER: 'customer/register-customer',
  CUSRTOMER_RATING_DETAILS: 'customer/rating/',
  EXISTED_CUSTOMER: 'customer/existed',
  CREATE_DIRECT_SALE_CUSTOMER: 'customer/create-direct-sale-customer',
  CUSTOMER_LAST_CONTACT_DATE: 'customer/last-contact/',
  CUSTOMER_DIRECT_SALE_USERS: 'customer/direct-sale-order-users/'
};

export type ApiEndpointProps = (typeof API_ENDPOINT)[keyof typeof API_ENDPOINT];
