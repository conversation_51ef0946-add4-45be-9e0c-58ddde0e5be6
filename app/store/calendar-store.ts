import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

interface CalendarStore {
  orders: OrderListAttributes[];
  setOrders: (orders: OrderListAttributes[]) => void;
  add: (order: OrderListAttributes) => void;
}

export const useCalendarStore = create<CalendarStore>()(
  persist(
    immer((set) => ({
      orders: [],
      setOrders: (orders) => {
        set({ orders });
      },
      add: () => {
        console.log(set);
      }
    })),
    {
      name: 'calendar-store'
    }
  )
);
