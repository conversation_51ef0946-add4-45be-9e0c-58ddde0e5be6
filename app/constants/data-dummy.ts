export const userData: UserManagementProps[] = [
  {
    id: '0001',
    name: '<PERSON>',
    role: 'User',
    phoneNumber: '**************',
    status: 'Active',
    date: '01 Jan, 2025'
  },
  {
    id: '0002',
    name: '<PERSON>',
    role: 'Admin',
    phoneNumber: '************',
    status: 'Active',
    date: '01 Jan, 2025'
  },
  {
    id: '0003',
    name: '<PERSON>',
    role: 'User',
    phoneNumber: '************',
    status: 'Deactivated',
    date: '02 Jan, 2025'
  },
  {
    id: '0004',
    name: '<PERSON>',
    role: 'Admin',
    phoneNumber: '************',
    status: 'Active',
    date: '02 Jan, 2025'
  },
  {
    id: '0005',
    name: '<PERSON>',
    role: 'Manager',
    phoneNumber: '************',
    status: 'Active',
    date: '02 Jan, 2025'
  },
  {
    id: '0006',
    name: '<PERSON>',
    role: 'Manager',
    phoneNumber: '************',
    status: 'Active',
    date: '02 Jan, 2025'
  },
  {
    id: '0007',
    name: '<PERSON><PERSON>',
    role: 'User',
    phoneNumber: '************',
    status: 'Active',
    date: '02 Jan, 2025'
  },
  {
    id: '0008',
    name: 'Floyd Ullrich',
    role: 'User',
    phoneNumber: '************',
    status: 'Active',
    date: '02 Jan, 2025'
  },
  {
    id: '0009',
    name: 'Drew Hettinger',
    role: 'User',
    phoneNumber: '************',
    status: 'Active',
    date: '02 Jan, 2025'
  },
  {
    id: '0010',
    name: 'Christian Blick MD',
    role: 'Admin',
    phoneNumber: '************',
    status: 'Active',
    date: '02 Jan, 2025'
  },
  {
    id: '0011',
    name: 'Ryan Moen',
    role: 'Admin',
    phoneNumber: '************',
    status: 'Active',
    date: '02 Jan, 2025'
  },
  {
    id: '0012',
    name: 'Brent Morar I',
    role: 'User',
    phoneNumber: '************',
    status: 'Active',
    date: '02 Jan, 2025'
  },
  {
    id: '0013',
    name: 'Christy Bins',
    role: 'User',
    phoneNumber: '************',
    status: 'Active',
    date: '02 Jan, 2025'
  },
  {
    id: '0014',
    name: 'Marsha Monahan',
    role: 'User',
    phoneNumber: '************',
    status: 'Active',
    date: '02 Jan, 2025'
  },
  {
    id: '0015',
    name: 'Curtis Breitenberg',
    role: 'User',
    phoneNumber: '************',
    status: 'Active',
    date: '02 Jan, 2025'
  },
  {
    id: '0016',
    name: 'Lori Nader',
    role: 'User',
    phoneNumber: '************',
    status: 'Active',
    date: '03 Jan, 2025'
  },
  {
    id: '0017',
    name: 'Tomasa Stiedemann',
    role: 'Admin',
    phoneNumber: '************',
    status: 'Deactivated',
    date: '03 Jan, 2025'
  },
  {
    id: '0018',
    name: 'Jerrod Mohr',
    role: 'Manager',
    phoneNumber: '************',
    status: 'Active',
    date: '03 Jan, 2025'
  },
  {
    id: '0019',
    name: 'Reina Tillman',
    role: 'User',
    phoneNumber: '************',
    status: 'Active',
    date: '03 Jan, 2025'
  },
  {
    id: '0020',
    name: 'Allan Schuppe',
    role: 'Admin',
    phoneNumber: '************',
    status: 'Active',
    date: '03 Jan, 2025'
  },
  {
    id: '0021',
    name: 'Irving Bosco',
    role: 'User',
    phoneNumber: '************',
    status: 'Deactivated',
    date: '04 Jan, 2025'
  },
  {
    id: '0022',
    name: 'Lia Schulist',
    role: 'Manager',
    phoneNumber: '************',
    status: 'Active',
    date: '04 Jan, 2025'
  },
  {
    id: '0023',
    name: 'Wilfredo Cummerata',
    role: 'Admin',
    phoneNumber: '************',
    status: 'Active',
    date: '04 Jan, 2025'
  },
  {
    id: '0024',
    name: 'Domenic Hilpert',
    role: 'User',
    phoneNumber: '************',
    status: 'Active',
    date: '04 Jan, 2025'
  },
  {
    id: '0025',
    name: 'Clifton Ledner',
    role: 'User',
    phoneNumber: '************',
    status: 'Active',
    date: '04 Jan, 2025'
  },
  {
    id: '0026',
    name: 'Alene Champlin',
    role: 'Manager',
    phoneNumber: '************',
    status: 'Active',
    date: '05 Jan, 2025'
  },
  {
    id: '0027',
    name: 'Junior Bechtelar',
    role: 'Admin',
    phoneNumber: '************',
    status: 'Deactivated',
    date: '05 Jan, 2025'
  },
  {
    id: '0028',
    name: "Wilma O'Hara",
    role: 'User',
    phoneNumber: '************',
    status: 'Active',
    date: '05 Jan, 2025'
  },
  {
    id: '0029',
    name: 'Matilde Zieme',
    role: 'Manager',
    phoneNumber: '************',
    status: 'Active',
    date: '05 Jan, 2025'
  },
  {
    id: '0030',
    name: 'Kiara Gulgowski',
    role: 'User',
    phoneNumber: '************',
    status: 'Active',
    date: '05 Jan, 2025'
  },
  {
    id: '0031',
    name: 'Dewey Smitham',
    role: 'Admin',
    phoneNumber: '************',
    status: 'Active',
    date: '06 Jan, 2025'
  },
  {
    id: '0032',
    name: 'Stuart Hermann',
    role: 'User',
    phoneNumber: '************',
    status: 'Deactivated',
    date: '06 Jan, 2025'
  },
  {
    id: '0033',
    name: 'Elsa Lockman',
    role: 'User',
    phoneNumber: '************',
    status: 'Active',
    date: '06 Jan, 2025'
  },
  {
    id: '0034',
    name: 'Leola Rogahn',
    role: 'User',
    phoneNumber: '************',
    status: 'Active',
    date: '06 Jan, 2025'
  },
  {
    id: '0035',
    name: 'Darrick Ritchie',
    role: 'Manager',
    phoneNumber: '************',
    status: 'Active',
    date: '06 Jan, 2025'
  },
  {
    id: '0036',
    name: 'Ora Leffler',
    role: 'Admin',
    phoneNumber: '************',
    status: 'Active',
    date: '06 Jan, 2025'
  },
  {
    id: '0037',
    name: 'Toni Lakin',
    role: 'User',
    phoneNumber: '************',
    status: 'Active',
    date: '07 Jan, 2025'
  },
  {
    id: '0038',
    name: "Alfredo O'Keefe",
    role: 'User',
    phoneNumber: '************',
    status: 'Deactivated',
    date: '07 Jan, 2025'
  },
  {
    id: '0039',
    name: 'Traci Kuhic',
    role: 'Manager',
    phoneNumber: '************',
    status: 'Active',
    date: '07 Jan, 2025'
  },
  {
    id: '0040',
    name: 'Nickolas Barton',
    role: 'Admin',
    phoneNumber: '************',
    status: 'Active',
    date: '07 Jan, 2025'
  },
  {
    id: '0041',
    name: 'Jaqueline Emard',
    role: 'User',
    phoneNumber: '************',
    status: 'Active',
    date: '07 Jan, 2025'
  },
  {
    id: '0042',
    name: 'Margarito Waelchi',
    role: 'Manager',
    phoneNumber: '************',
    status: 'Active',
    date: '07 Jan, 2025'
  },
  {
    id: '0043',
    name: 'Mariano Feest',
    role: 'User',
    phoneNumber: '************',
    status: 'Active',
    date: '08 Jan, 2025'
  },
  {
    id: '0044',
    name: 'Luann Collins',
    role: 'Admin',
    phoneNumber: '************',
    status: 'Active',
    date: '08 Jan, 2025'
  },
  {
    id: '0045',
    name: 'Francisca Emmerich',
    role: 'User',
    phoneNumber: '************',
    status: 'Deactivated',
    date: '08 Jan, 2025'
  },
  {
    id: '0046',
    name: 'Lenny Bernier',
    role: 'Manager',
    phoneNumber: '************',
    status: 'Active',
    date: '08 Jan, 2025'
  },
  {
    id: '0047',
    name: 'Judy Kautzer',
    role: 'User',
    phoneNumber: '************',
    status: 'Active',
    date: '08 Jan, 2025'
  },
  {
    id: '0048',
    name: 'Rosalva Doyle',
    role: 'User',
    phoneNumber: '************',
    status: 'Active',
    date: '08 Jan, 2025'
  },
  {
    id: '0049',
    name: 'Edna Rice',
    role: 'Admin',
    phoneNumber: '************',
    status: 'Active',
    date: '08 Jan, 2025'
  },
  {
    id: '0050',
    name: 'Rufus Johns',
    role: 'User',
    phoneNumber: '************',
    status: 'Active',
    date: '08 Jan, 2025'
  }
];
export const categoryData: CategoryProps[] = [
  {
    id: 'cat_001',
    image: 'https://randomuser.me/api/portraits/men/1.jpg',
    name: 'Electronics',
    status: 'Active',
    date: new Date('2024-01-15T10:00:00Z')
  },
  {
    id: 'cat_002',
    image: 'https://randomuser.me/api/portraits/men/2.jpg',
    name: 'Books',
    status: 'Active',
    date: new Date('2023-11-20T14:30:00Z')
  },
  {
    id: 'cat_003',
    image: 'https://randomuser.me/api/portraits/men/3.jpg',
    name: 'Clothing',
    status: 'Inactive',
    date: new Date('2024-03-01T09:15:00Z')
  },
  {
    id: 'cat_004',
    image: 'https://randomuser.me/api/portraits/men/4.jpg',
    name: 'Home Goods',
    status: 'Active',
    date: new Date('2023-09-10T11:00:00Z')
  },
  {
    id: 'cat_005',
    image: 'https://randomuser.me/api/portraits/men/5.jpg',
    name: 'Sports & Outdoors',
    status: 'Active',
    date: new Date('2024-02-28T16:00:00Z')
  },
  {
    id: 'cat_006',
    image: 'https://randomuser.me/api/portraits/men/6.jpg',
    name: 'Toys & Games',
    status: 'Inactive',
    date: new Date('2024-04-05T10:30:00Z')
  },
  {
    id: 'cat_007',
    image: 'https://randomuser.me/api/portraits/men/7.jpg',
    name: 'Food & Groceries',
    status: 'Active',
    date: new Date('2023-10-25T08:45:00Z')
  },
  {
    id: 'cat_008',
    image: 'https://randomuser.me/api/portraits/men/8.jpg',
    name: 'Automotive',
    status: 'Active',
    date: new Date('2024-01-01T13:00:00Z')
  },
  {
    id: 'cat_009',
    image: 'https://randomuser.me/api/portraits/men/9.jpg',
    name: 'Health & Beauty',
    status: 'Active',
    date: new Date('2024-05-18T15:00:00Z')
  },
  {
    id: 'cat_010',
    image: 'https://randomuser.me/api/portraits/men/10.jpg',
    name: 'Garden & Patio',
    status: 'Inactive',
    date: new Date('2023-07-01T09:00:00Z')
  },
  {
    id: 'cat_011',
    image: 'https://randomuser.me/api/portraits/men/11.jpg',
    name: 'Jewelry',
    status: 'Active',
    date: new Date('2024-06-20T10:00:00Z')
  },
  {
    id: 'cat_012',
    image: 'https://randomuser.me/api/portraits/men/12.jpg',
    name: 'Pet Supplies',
    status: 'Active',
    date: new Date('2023-12-12T17:00:00Z')
  },
  {
    id: 'cat_013',
    image: 'https://randomuser.me/api/portraits/men/13.jpg',
    name: 'Arts & Crafts',
    status: 'Active',
    date: new Date('2024-07-01T11:00:00Z')
  },
  {
    id: 'cat_014',
    image: 'https://randomuser.me/api/portraits/men/14.jpg',
    name: 'Baby Products',
    status: 'Inactive',
    date: new Date('2023-08-01T14:00:00Z')
  },
  {
    id: 'cat_015',
    image: 'https://randomuser.me/api/portraits/men/15.jpg',
    name: 'Music & Instruments',
    status: 'Active',
    date: new Date('2024-02-14T09:00:00Z')
  },
  {
    id: 'cat_016',
    image: 'https://randomuser.me/api/portraits/men/16.jpg',
    name: 'Software',
    status: 'Active',
    date: new Date('2024-05-05T12:00:00Z')
  },
  {
    id: 'cat_017',
    image: 'https://randomuser.me/api/portraits/men/17.jpg',
    name: 'Travel Gear',
    status: 'Inactive',
    date: new Date('2023-10-01T10:00:00Z')
  }
];
export const categoryAddOnRecords: CategoryAddOnProps[] = [
  {
    id: 'ca001',
    image: 'https://randomuser.me/api/portraits/thumb/men/1.jpg',
    name: 'Sauces',
    status: 'Active',
    variants: [
      {
        id: 'v001',
        image: 'https://randomuser.me/api/portraits/thumb/women/2.jpg',
        name: 'Ketchup',
        price: '0.50'
      },
      {
        id: 'v002',
        image: 'https://randomuser.me/api/portraits/thumb/men/3.jpg',
        name: 'Mustard',
        price: '0.50'
      },
      {
        id: 'v003',
        image: 'https://randomuser.me/api/portraits/thumb/women/4.jpg',
        name: 'BBQ Sauce',
        price: '0.75'
      }
    ],
    date: new Date('2024-01-15T10:00:00Z')
  },
  {
    id: 'ca002',
    image: 'https://randomuser.me/api/portraits/thumb/women/5.jpg',
    name: 'Drinks',
    status: 'Active',
    variants: [
      {
        id: 'v004',
        image: 'https://randomuser.me/api/portraits/thumb/men/6.jpg',
        name: 'Coca-Cola',
        price: '2.00'
      },
      {
        id: 'v005',
        image: 'https://randomuser.me/api/portraits/thumb/women/7.jpg',
        name: 'Sprite',
        price: '2.00'
      },
      {
        id: 'v006',
        image: 'https://randomuser.me/api/portraits/thumb/men/8.jpg',
        name: 'Bottled Water',
        price: '1.50'
      }
    ],
    date: new Date('2024-02-20T11:30:00Z')
  },
  {
    id: 'ca003',
    image: 'https://randomuser.me/api/portraits/thumb/men/9.jpg',
    name: 'Cheese Options',
    status: 'Active',
    variants: [
      {
        id: 'v007',
        image: 'https://randomuser.me/api/portraits/thumb/women/10.jpg',
        name: 'Cheddar Slice',
        price: '1.00'
      },
      {
        id: 'v008',
        image: 'https://randomuser.me/api/portraits/thumb/men/11.jpg',
        name: 'Mozzarella Shreds',
        price: '1.25'
      }
    ],
    date: new Date('2024-03-01T09:15:00Z')
  },
  {
    id: 'ca004',
    image: 'https://randomuser.me/api/portraits/thumb/women/12.jpg',
    name: 'Pizza Toppings',
    status: 'Active',
    variants: [
      {
        id: 'v009',
        image: 'https://randomuser.me/api/portraits/thumb/men/13.jpg',
        name: 'Pepperoni',
        price: '1.50'
      },
      {
        id: 'v010',
        image: 'https://randomuser.me/api/portraits/thumb/women/14.jpg',
        name: 'Mushrooms',
        price: '1.00'
      },
      {
        id: 'v011',
        image: 'https://randomuser.me/api/portraits/thumb/men/15.jpg',
        name: 'Black Olives',
        price: '0.75'
      },
      {
        id: 'v012',
        image: 'https://randomuser.me/api/portraits/thumb/women/16.jpg',
        name: 'Red Onions',
        price: '0.75'
      }
    ],
    date: new Date('2024-04-10T14:00:00Z')
  },
  {
    id: 'ca005',
    image: 'https://randomuser.me/api/portraits/thumb/men/17.jpg',
    name: 'Side Dishes',
    status: 'Active',
    variants: [
      {
        id: 'v013',
        image: 'https://randomuser.me/api/portraits/thumb/women/18.jpg',
        name: 'French Fries',
        price: '3.00'
      },
      {
        id: 'v014',
        image: 'https://randomuser.me/api/portraits/thumb/men/19.jpg',
        name: 'Side Salad',
        price: '3.50'
      }
    ],
    date: new Date('2024-05-05T16:45:00Z')
  },
  {
    id: 'ca006',
    image: 'https://randomuser.me/api/portraits/thumb/women/20.jpg',
    name: 'Desserts',
    status: 'Inactive',
    variants: [
      {
        id: 'v015',
        image: 'https://randomuser.me/api/portraits/thumb/men/21.jpg',
        name: 'Chocolate Brownie',
        price: '4.00'
      },
      {
        id: 'v016',
        image: 'https://randomuser.me/api/portraits/thumb/women/22.jpg',
        name: 'Vanilla Ice Cream',
        price: '3.00'
      }
    ],
    date: new Date('2024-06-12T08:00:00Z')
  },
  {
    id: 'ca007',
    image: 'https://randomuser.me/api/portraits/thumb/men/23.jpg',
    name: 'Bread Choices',
    status: 'Active',
    variants: [
      {
        id: 'v017',
        image: 'https://randomuser.me/api/portraits/thumb/women/24.jpg',
        name: 'White Bread',
        price: '0.75'
      },
      {
        id: 'v018',
        image: 'https://randomuser.me/api/portraits/thumb/men/25.jpg',
        name: 'Wheat Bread',
        price: '0.85'
      }
    ],
    date: new Date('2024-07-25T13:00:00Z')
  }
];
export const productData: ProductProps[] = [
  {
    id: 'prod-1',
    name: 'Condo & Apartment',
    category: [categoryData[0], categoryData[2], categoryData[3]], // Electronics
    status: 'Active',
    date: new Date('2024-06-01T09:00:00Z')
  },
  {
    id: 'prod-2',
    name: 'Flat & Shop House',
    category: [categoryData[1]], // Fashion
    status: 'Active',
    date: new Date('2024-06-05T14:00:00Z')
  },
  {
    id: 'prod-3',
    name: 'Villa',
    category: [categoryData[2]], // Home & Garden
    status: 'Active',
    date: new Date('2024-06-10T10:30:00Z')
  },
  {
    id: 'prod-4',
    name: 'General Cleaning',
    category: [categoryData[3]], // Books
    status: 'Active',
    date: new Date('2024-06-15T11:00:00Z')
  },
  {
    id: 'prod-5',
    name: 'Deep Cleaning',
    category: [categoryData[4]], // Sports & Outdoors (Inactive category, perhaps still visible but unpurchasable)
    status: 'Inactive',
    date: new Date('2024-06-20T13:00:00Z')
  }
];
export const productOptions: ProductOptionProps[] = [
  {
    id: '1',
    name: 'Basic Plan',
    price: '9.99',
    status: 'Active',
    date: new Date('2025-01-01')
  },
  {
    id: '2',
    name: 'Standard Plan',
    price: '19.99',
    status: 'Active',
    date: new Date('2025-01-10')
  },
  {
    id: '3',
    name: 'Premium Plan',
    price: '29.99',
    status: 'Inactive',
    date: new Date('2025-01-15')
  },
  {
    id: '4',
    name: 'Enterprise Plan',
    price: '49.99',
    status: 'Active',
    date: new Date('2025-02-01')
  },
  {
    id: '5',
    name: 'Trial Pack',
    price: '0.00',
    status: 'Active',
    date: new Date('2025-02-05')
  },
  {
    id: '6',
    name: 'Student Plan',
    price: '5.99',
    status: 'Inactive',
    date: new Date('2025-02-10')
  },
  {
    id: '7',
    name: 'Business Plan',
    price: '39.99',
    status: 'Active',
    date: new Date('2025-02-15')
  },
  {
    id: '8',
    name: 'Starter Kit',
    price: '14.99',
    status: 'Active',
    date: new Date('2025-03-01')
  },
  {
    id: '9',
    name: 'Growth Plan',
    price: '24.99',
    status: 'Inactive',
    date: new Date('2025-03-05')
  },
  {
    id: '10',
    name: 'Marketing Add-on',
    price: '4.99',
    status: 'Active',
    date: new Date('2025-03-10')
  },
  {
    id: '11',
    name: 'Analytics Add-on',
    price: '3.99',
    status: 'Inactive',
    date: new Date('2025-03-12')
  },
  {
    id: '12',
    name: 'AI Feature Pack',
    price: '12.00',
    status: 'Active',
    date: new Date('2025-03-15')
  },
  {
    id: '13',
    name: 'Security Suite',
    price: '7.50',
    status: 'Active',
    date: new Date('2025-03-20')
  },
  {
    id: '14',
    name: 'Data Export',
    price: '2.00',
    status: 'Inactive',
    date: new Date('2025-03-25')
  },
  {
    id: '15',
    name: 'Support Package',
    price: '6.00',
    status: 'Active',
    date: new Date('2025-04-01')
  },
  {
    id: '16',
    name: 'Cloud Storage 100GB',
    price: '8.00',
    status: 'Active',
    date: new Date('2025-04-05')
  },
  {
    id: '17',
    name: 'Cloud Storage 1TB',
    price: '15.00',
    status: 'Inactive',
    date: new Date('2025-04-10')
  },
  {
    id: '18',
    name: 'Video Conferencing',
    price: '10.00',
    status: 'Active',
    date: new Date('2025-04-15')
  },
  {
    id: '19',
    name: 'API Access',
    price: '20.00',
    status: 'Active',
    date: new Date('2025-04-20')
  },
  {
    id: '20',
    name: 'Team Collaboration',
    price: '11.00',
    status: 'Inactive',
    date: new Date('2025-04-25')
  },
  {
    id: '21',
    name: 'Onboarding Service',
    price: '30.00',
    status: 'Active',
    date: new Date('2025-05-01')
  },
  {
    id: '22',
    name: 'Customization Pack',
    price: '25.00',
    status: 'Inactive',
    date: new Date('2025-05-05')
  }
];
export const dummyOrders: OrderComponentProps[] = [
  {
    id: '#100001',
    services: 2,
    date: '5 Aug, 2025',
    status: 'Pending',
    imageSrc: 'https://randomuser.me/api/portraits/thumb/men/1.jpg'
  },
  {
    id: '#100002',
    services: 3,
    date: '6 Aug, 2025',
    status: 'In Progress',
    imageSrc: 'https://randomuser.me/api/portraits/thumb/men/1.jpg'
  },
  {
    id: '#100003',
    services: 5,
    date: '7 Aug, 2025',
    status: 'Completed',
    imageSrc: 'https://randomuser.me/api/portraits/thumb/men/1.jpg'
  },
  {
    id: '#100004',
    services: 1,
    date: '8 Aug, 2025',
    status: 'Pending',
    imageSrc: 'https://randomuser.me/api/portraits/thumb/men/1.jpg'
  },
  {
    id: '#100005',
    services: 4,
    date: '9 Aug, 2025',
    status: 'In Progress',
    imageSrc: 'https://randomuser.me/api/portraits/thumb/men/1.jpg'
  },
  {
    id: '#100006',
    services: 2,
    date: '10 Aug, 2025',
    status: 'Pending',
    imageSrc: 'https://randomuser.me/api/portraits/thumb/women/2.jpg'
  },
  {
    id: '#100007',
    services: 3,
    date: '11 Aug, 2025',
    status: 'In Progress',
    imageSrc: 'https://randomuser.me/api/portraits/thumb/men/3.jpg'
  },
  {
    id: '#100006',
    services: 2,
    date: '10 Aug, 2025',
    status: 'Pending',
    imageSrc: 'https://randomuser.me/api/portraits/thumb/women/2.jpg'
  },
  {
    id: '#100007',
    services: 3,
    date: '11 Aug, 2025',
    status: 'In Progress',
    imageSrc: 'https://randomuser.me/api/portraits/thumb/men/3.jpg'
  }
];

export interface OrderDetail {
  orderId: string;
  name: string;
  address: string;
  image: string;
  country_code: string;
  phone_number: string;
  date: string;
  time?: string;
  duration?: string;
  status: 'pending' | 'paid' | 'unpaid';
  lat: number;
  lng: number;
}

export const orderDetails: Record<string, OrderDetail> = {
  '#100003': {
    orderId: '#100003',
    name: 'Reth Ny Phanith',
    address: 'Google Building 43, 43 Amphitheatre Pkwy, Mountain View, CA 94043, USA',
    image:
      'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
    country_code: '+855',
    phone_number: '175 011 33',
    date: '2025-07-24',
    time: '9:00 AM',
    duration: '4Hours',
    status: 'paid',
    lat: 11.5727473,
    lng: 104.9051283
  },
  '#100004': {
    orderId: '#100004',
    name: 'Jane Smith',
    address: '456 Oak Avenue, Springfield, IL 62701, USA',
    image:
      'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
    country_code: '+855',
    phone_number: '************',
    date: '2025-08-04',
    time: '2:00 PM',
    duration: '3Hours',
    status: 'pending',
    lat: 11.5727473,
    lng: 104.9051283
  },
  '#100005': {
    orderId: '#100005',
    name: 'Alice Johnson',
    address: '789 Pine Road, Austin, TX 78701, USA',
    image:
      'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
    country_code: '+855',
    phone_number: '************',
    date: '2025-08-03',
    time: '10:00 AM',
    duration: '2Hours',
    status: 'unpaid',
    lat: 11.5727473,
    lng: 104.9051283
  },
  '#100001': {
    orderId: '#100001',
    name: 'Michael Chen',
    address: '321 Elm Street, Seattle, WA 98101, USA',
    image:
      'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
    country_code: '+855',
    phone_number: '************',
    date: '2025-08-02',
    time: '11:00 AM',
    duration: '5Hours',
    status: 'paid',
    lat: 11.5727473,
    lng: 104.9051283
  },
  '#100002': {
    orderId: '#100002',
    name: 'Sarah Wilson',
    address: '654 Maple Drive, Denver, CO 80201, USA',
    image:
      'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face',
    country_code: '+855',
    phone_number: '************',
    date: '2025-08-01',
    time: '3:30 PM',
    duration: '4Hours',
    status: 'pending',
    lat: 11.5727473,
    lng: 104.9051283
  }
};

export const datadummy = [
  { id: 'bathroom', label: 'Bathroom', checked: false },
  { id: 'living-room', label: 'Living Room', checked: true },
  { id: 'kitchen', label: 'Kitchen', checked: false },
  { id: 'storage-room', label: 'Storage Room', checked: true },
  { id: 'balcony', label: 'Balcony', checked: false },
  { id: 'fridge', label: 'Fridge', checked: false },
  { id: 'curtain', label: 'Curtain', checked: true },
  { id: 'sofa', label: 'Sofa', checked: false }
];

export const serviceDetailsDummy: ServiceDetailsProps = {
  id: 'svc-001',
  serviceName: 'Service 1',
  category: 'Home Cleaning',
  addOns: 1,
  price: 100.0,
  imageSrc: 'https://randomuser.me/api/portraits/thumb/men/25.jpg',
  items: [
    { id: '1', name: 'Bathroom', checked: false },
    { id: '2', name: 'Living Room', checked: false },
    { id: '3', name: 'Kitchen', checked: false },
    { id: '4', name: 'Storage Room', checked: false },
    { id: '5', name: 'Swimming Pool', checked: false },
    { id: '6', name: 'Balcony', checked: false },
    { id: '7', name: 'Lamp or Light', checked: false },
    { id: '8', name: 'Floor', checked: false },
    { id: '9', name: 'Aircon', checked: false },
    { id: '10', name: 'Sofa', checked: false },
    { id: '11', name: 'Cabinet', checked: false },
    { id: '12', name: 'Fridge', checked: false },
    { id: '13', name: 'Curtain', checked: false },
    { id: '14', name: 'Elevator', checked: false },
    { id: '15', name: 'Carpet', checked: false },
    { id: '16', name: 'Laundry', checked: false },
    { id: '17', name: 'Cobwebs', checked: false },
    { id: '18', name: 'Mattress', checked: false },
    { id: '19', name: 'Windows', checked: false },
    { id: '20', name: 'Wall', checked: false },
    { id: '21', name: 'Ceiling', checked: false },
    { id: '22', name: 'Stair', checked: false },
    { id: '23', name: 'Mirror', checked: false },
    { id: '24', name: 'Roof', checked: false }
  ] as ServiceItem[]
};
export const PaginationDummy: PaginationDemoProps[] = [
  {
    currentPage: 1,
    totalPages: 10
  }
];

export const popupOptions: popupOptionProps[] = [
  {
    id: 'Pickup',
    title: 'Are you sure you want to pick up this case?',
    description:
      'Once confirmed, you won’t be able to edit this order - but you can still cancel it if needed.',
    confirmText: 'Pick Up',
    cancelText: 'Cancel',
    inputPlaceholder: false,
    variant: 'default'
  },

  {
    id: 'CancelOrder',
    title: 'Mind sharing why you’re canceling this order?',
    description: 'The customer is causing delays and impacting our team’s productivity',
    confirmText: 'Yes, Cancel',
    cancelText: 'No, Keep Order',
    inputPlaceholder: true,
    variant: 'destructive'
  },

  {
    id: 'Refund',
    title: 'You’re about to refund ${amount} customer',
    description:
      'This action is permanent and cannot be undone. Continue only if you’re certain about proceeding with the refund.',
    confirmText: 'Refund',
    cancelText: 'Cancel',
    inputPlaceholder: false,
    variant: 'default'
  },

  {
    id: 'SubmitOrder',
    title: 'Are you sure you want to submit this order?',
    description:
      'You won’t be able to edit it later. If needed, please contact your supervisor for assistance.',
    confirmText: 'Submit Now',
    cancelText: 'Cancel',
    inputPlaceholder: false,
    variant: 'default'
  },

  {
    id: 'MarkComplete',
    title: 'Are you sure you want to mark this order as complete?',
    description:
      'You can still make changes later, but only after a short review period. Make sure everything looks good for now before submitting.',
    confirmText: 'Mark as Complete',
    cancelText: 'Cancel',
    inputPlaceholder: false,
    variant: 'success'
  },

  {
    id: 'EditCompleted',
    title: 'Edit Completed Order',
    inputPlaceholder: false,
    description: 'Please provide details for editing the completed order',
    confirmText: 'Save',
    cancelText: 'Cancel',
    variant: 'default'
  }
];

export const serviceDetailsPopupDummy = {
  id: 'service-001',
  serviceTitle: 'Service 1',
  isOpen: true,
  formData: {
    serviceDetails: [
      { id: '1', name: 'Wash Car', checked: true },
      { id: '2', name: 'Vacuum Interior', checked: false },
      { id: '3', name: 'Wash Car', checked: true },
      { id: '4', name: 'Wash Car', checked: true },
      { id: '5', name: 'Wash Car', checked: true },
      { id: '6', name: 'Wash Car', checked: true },
      { id: '7', name: 'Wash Car', checked: false },
      { id: '8', name: 'Wash Car', checked: false },
      { id: '9', name: 'Wash Car', checked: false },
      { id: '10', name: 'Wash Car', checked: false },
      { id: '11', name: 'Wash Car', checked: false },
      { id: '12', name: 'Wash Car', checked: true }
    ],
    serviceAddOn: [
      {
        id: 'addon-1',
        product: '',
        variant: '',
        quantity: ''
      }
    ],
    paymentInformation: {
      id: 'payment-1',
      paymentStatus: '',
      paymentMethod: '',
      note: ''
    }
  },
  products: [
    { value: 'Wax', label: 'Wax' },
    { value: 'Polish', label: 'Polish' }
  ],
  variants: [
    { value: 'Gloss', label: 'Gloss' },
    { value: 'Matte', label: 'Matte' }
  ],
  quantities: [
    { value: '1', label: '1' },
    { value: '2', label: '2' }
  ],
  paymentStatuses: [
    { value: 'Paid', label: 'Paid' },
    { value: 'Pending', label: 'Pending' }
  ],
  paymentMethods: [
    { value: 'Credit Card', label: 'Credit Card' },
    { value: 'Cash', label: 'Cash' }
  ],
  onClose: () => {},
  onChange: () => {}
};

export const dummyOrderCompleteService: OrderCompleteServiceProps[] = [
  {
    id: 'order123',
    service_name: 'Service 1',
    category: 'Home Cleaning',
    price: 18,
    addOn: 2,
    imageSrc: 'https://randomuser.me/api/portraits/thumb/men/25.jpg',
    CompleteserviceDetails: [
      { id: '1', label: 'Kitchen', value: 7 },
      { id: '2', label: 'Bedroom', value: 3 },
      { id: '3', label: 'Living Room', value: 1 },
      { id: '4', label: 'Item', value: 6 },
      { id: '5', label: 'Bathroom', value: 2 }
    ]
  },
  {
    id: 'order456',
    service_name: 'Service 2',
    category: 'Office Cleaning',
    price: 25,
    addOn: 1,
    imageSrc: 'https://randomuser.me/api/portraits/thumb/women/45.jpg',
    CompleteserviceDetails: [
      { id: '1', label: 'Kitchen', value: 5 },
      { id: '2', label: 'Conference Room', value: 2 },
      { id: '3', label: 'Workstation', value: 10 },
      { id: '4', label: 'Item', value: 4 },
      { id: '5', label: 'Restroom', value: 3 }
    ]
  }
];
export interface VoucherProps {
  id: string;
  name: string;
  code: string;
  discountType: 'Amount' | 'Percentage';
  discountValue: number;
  usageLimit: number;
  perUserLimit: number;
  status: 'Active' | 'Inactive';
  validFrom: Date;
  validTo: Date;
  eligibleServices: string;
  eligibleUsers: string;
}

// Dummy data for testing
export const voucherdummy: VoucherProps[] = [
  {
    id: '1',
    name: 'Summer Sale',
    code: 'SUMMER25',
    discountType: 'Percentage',
    discountValue: 25,
    usageLimit: 100,
    perUserLimit: 2,
    status: 'Active',
    validFrom: new Date('2025-06-01'),
    validTo: new Date('2025-06-30'),
    eligibleServices: 'All Services',
    eligibleUsers: 'All Users'
  },
  {
    id: '2',
    name: 'New Year Discount',
    code: 'NY2025',
    discountType: 'Amount',
    discountValue: 50,
    usageLimit: 50,
    perUserLimit: 1,
    status: 'Inactive',
    validFrom: new Date('2025-01-01'),
    validTo: new Date('2025-01-15'),
    eligibleServices: 'All Services',
    eligibleUsers: 'All Users'
  },
  {
    id: '3',
    name: 'First Purchase Offer',
    code: 'WELCOME10',
    discountType: 'Percentage',
    discountValue: 10,
    usageLimit: 200,
    perUserLimit: 1,
    status: 'Active',
    validFrom: new Date('2025-02-01'),
    validTo: new Date('2025-02-15'),
    eligibleServices: 'Service A',
    eligibleUsers: 'User Group 1'
  }
];
export const servicesData: ServiceDetailsInProgressProps[] = [
  {
    id: '1',
    serviceName: 'Service 1',
    category: 'Household Services',
    addOns: 1,
    price: 100,
    imageSrc:
      'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face',
    Serviceitems: [
      { description: 'Living room cleaning', quantity: 1 },
      { description: 'Bedroom cleaning', quantity: 2 },
      { description: 'Carpet vacuuming', quantity: 2 },
      { description: 'Carpet vacuuming', quantity: 2 }
    ] as InprogressServiceDetail[]
  },
  {
    id: '2',
    serviceName: 'Service 2',
    category: 'Commercial Services',
    addOns: 2,
    price: 150,
    imageSrc:
      'https://images.unsplash.com/photo-1502764613149-7f1d229e2305?w=150&h=150&fit=crop&crop=face',
    Serviceitems: [
      { description: 'Desk cleaning', quantity: '' },
      { description: 'Carpet vacuuming', quantity: 2 },
      { description: 'Window wiping', quantity: 4 },
      { description: 'Window wiping', quantity: 4 },

      { description: 'Window wiping', quantity: 4 }
    ] as InprogressServiceDetail[]
  },
  {
    id: '3',
    serviceName: 'Service 3',
    category: 'Automotive Services',
    addOns: 0,
    price: 50,
    imageSrc:
      'https://images.unsplash.com/photo-1503376780353-7e6692767b70?w=150&h=150&fit=crop&crop=face',
    Serviceitems: [
      { description: 'Exterior wash', quantity: 1 },
      { description: 'Interior vacuum', quantity: 1 }
    ] as InprogressServiceDetail[]
  }
];
export const dummyTopupData: TopupProps[] = [
  {
    transactionId: '01 Jan, 2025 10:00AM',
    topupAmount: 50.0,
    paymentMethod: 'Credit Card',
    status: 'Completed',
    date: '2025-08-01',
    remark: ''
  },
  {
    transactionId: '02 Jan, 2025 10:00AM',
    topupAmount: 20.5,
    paymentMethod: 'PayPal',
    status: 'Pending',
    date: '2025-08-02',
    remark: 'Pending payment verification'
  },
  {
    transactionId: '03 Jan, 2025 10:00AM',
    topupAmount: 100.0,
    paymentMethod: 'Bank Transfer',
    status: 'Cancelled',
    date: '2025-08-03',
    remark: 'User cancelled the transaction'
  },
  {
    transactionId: '04 Jan, 2025 10:00AM',
    topupAmount: 75.25,
    paymentMethod: 'Credit Card',
    status: 'Completed',
    date: '2025-08-04',
    remark: 'Promo bonus included'
  },
  {
    transactionId: '05 Jan, 2025 10:00AM',
    topupAmount: 10.0,
    paymentMethod: 'PayPal',
    status: 'Completed',
    date: '2025-08-05',
    remark: 'Small balance top-up'
  },
  {
    transactionId: '06 Jan, 2025 10:00AM',
    topupAmount: 50.0,
    paymentMethod: 'Credit Card',
    status: 'Completed',
    date: '2025-08-01',
    remark: ''
  },
  {
    transactionId: '07 Jan, 2025 10:00AM',
    topupAmount: 20.5,
    paymentMethod: 'PayPal',
    status: 'Pending',
    date: '2025-08-02',
    remark: 'Pending payment verification'
  }
];

export const bannerData: BannerProps[] = [
  {
    id: 'prod-1',
    name: 'Condo & Apartment',
    imageSrc:
      'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?auto=format&fit=crop&w=160&h=90&q=80',
    url: 'https://example.com/condo-apartment',
    status: 'Active'
  },
  {
    id: 'prod-2',
    name: 'Flat & Shop House',
    imageSrc:
      'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?auto=format&fit=crop&w=160&h=90&q=80',
    url: 'https://example.com/condo-apartment',
    status: 'Active'
  },
  {
    id: 'prod-3',
    name: 'Villa',
    imageSrc:
      'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?auto=format&fit=crop&w=160&h=90&q=80',
    url: 'https://example.com/condo-apartment',
    status: 'Active'
  },
  {
    id: 'prod-4',
    name: 'General Cleaning',
    imageSrc:
      'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?auto=format&fit=crop&w=160&h=90&q=80',
    url: 'https://example.com/condo-apartment',
    status: 'Active'
  },
  {
    id: 'prod-5',
    name: 'Deep Cleaning',
    imageSrc:
      'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?auto=format&fit=crop&w=160&h=90&q=80',
    status: 'Active',
    url: 'https://example.com/condo-apartment'
  }
];
export const promotion: PromotionProps[] = [
  {
    id: 'promo001',
    name: 'Summer Sale',
    applies_to: 'Products',
    promotion_type: 'Percentage',
    value: '15%',
    usage_limit: 100,
    per_user_unit: 2,
    status: 'Active',
    created_by: 'admin',
    valid_from: '2025-06-01',
    valid_to: '2025-08-31',
    eligible_service: 'All Services',
    eligible_users: 'Registered Users'
  },
  {
    id: 'promo002',
    name: 'New User Discount',
    applies_to: 'Services',
    promotion_type: 'Amount',
    value: '$10',
    usage_limit: 50,
    per_user_unit: 1,
    status: 'Inactive',
    created_by: 'marketing',
    valid_from: '2025-01-01',
    valid_to: '2025-12-31',
    eligible_service: 'Consulting',
    eligible_users: 'New Users'
  },
  {
    id: 'promo003',
    name: 'Holiday Bundle',
    applies_to: 'Both',
    promotion_type: 'Percentage',
    value: '25%',
    usage_limit: 200,
    per_user_unit: 3,
    status: 'Inactive',
    created_by: 'admin',
    valid_from: '2024-12-01',
    valid_to: '2025-01-05',
    eligible_service: 'Premium Services',
    eligible_users: 'All Users'
  }
];
export const serviceBundleData: ServiceBundleProps[] = [
  {
    id: 'sb001',
    name: 'Premium Cleaning Bundle',
    status: 'Active',
    bundleType: 'Cleaning',
    date: new Date('2025-01-10')
  },
  {
    id: 'sb002',
    name: 'Standard Maintenance Bundle',
    status: 'In Active',
    bundleType: 'Maintenance',
    date: new Date('2024-12-15')
  },
  {
    id: 'sb003',
    name: 'Express Repair Bundle',
    status: 'Active',
    bundleType: 'Repair',
    date: new Date('2025-03-05')
  },
  {
    id: 'sb004',
    name: 'Complete Care Bundle',
    status: 'Active',
    bundleType: 'Full Service',
    date: new Date('2025-06-20')
  },
  {
    id: 'sb005',
    name: 'Basic Checkup Bundle',
    status: 'In Active',
    bundleType: 'Checkup',
    date: new Date('2024-11-30')
  }
];
// export const pushNotificationData: PushNotificationProps[] = [
//   {
//     id: 'prod-1',
//     name: 'Condo & Apartment',
//     status: 'Active',
//     description:
//       'Explore o e our range of condos  o e our range of condos e our range of condos and apartments hhhhhhhe our range of condos and apartments hhhhhhh  for rent or purchase.lore our range of condo',
//     activeTo: new Date('2024-06-20T13:00:00Z'),
//     activeFrom: new Date('2024-06-20T13:00:00Z')
//   },
//   {
//     id: 'prod-2',
//     name: 'Luxury Villa Promotion',
//     status: 'Active',
//     description:
//       'Discover exclusive deals on luxury villas with stunning views and premium amenities.',
//     activeFrom: new Date('2024-07-01T09:00:00Z'),
//     activeTo: new Date('2024-07-15T23:59:00Z')
//   },
//   {
//     id: 'prod-3',
//     name: 'New Tenant Discount',
//     status: 'Active',
//     description: 'Sign a lease this month and get 10% off your first three months’ rent!',
//     activeFrom: new Date('2024-08-01T00:00:00Z'),
//     activeTo: new Date('2024-08-31T23:59:00Z')
//   },
//   {
//     id: 'prod-4',
//     name: 'Downtown Loft Launch',
//     status: 'Active',
//     description: 'Be the first to tour our newly renovated lofts in the heart of the city.',
//     activeFrom: new Date('2024-09-10T10:00:00Z'),
//     activeTo: new Date('2024-09-30T18:00:00Z')
//   },
//   {
//     id: 'prod-5',
//     name: 'Holiday Home Special',
//     status: 'Active',
//     description: 'Book a holiday home for the season and enjoy complimentary cleaning services.',
//     activeFrom: new Date('2024-11-01T08:00:00Z'),
//     activeTo: new Date('2024-12-15T23:59:00Z')
//   }
// ];

export const referralDummy: ReferralProgramProps[] = [
  {
    id: '1',
    name: 'Alice Johnson',
    email: '<EMAIL>',
    referralCode: 101,
    totalReferralUser: 5,
    pointEarnd: 50,
    date: new Date('2025-01-10')
  },
  {
    id: '2',
    name: 'Bob Smith',
    email: '<EMAIL>',
    referralCode: 102,
    totalReferralUser: 3,
    pointEarnd: 30,
    date: new Date('2025-01-12')
  },
  {
    id: '3',
    name: 'Charlie Brown',
    email: '<EMAIL>',
    referralCode: 103,
    totalReferralUser: 8,
    pointEarnd: 80,
    date: new Date('2025-01-15')
  },
  {
    id: '4',
    name: 'Diana Prince',
    email: '<EMAIL>',
    referralCode: 104,
    totalReferralUser: 2,
    pointEarnd: 20,
    date: new Date('2025-01-18')
  },
  {
    id: '5',
    name: 'Ethan Hunt',
    email: '<EMAIL>',
    referralCode: 105,
    totalReferralUser: 6,
    pointEarnd: 60,
    date: new Date('2025-01-20')
  },
  {
    id: '6',
    name: 'Fiona Gallagher',
    email: '<EMAIL>',
    referralCode: 106,
    totalReferralUser: 4,
    pointEarnd: 40,
    date: new Date('2025-01-22')
  },
  {
    id: '7',
    name: 'George Martin',
    email: '<EMAIL>',
    referralCode: 107,
    totalReferralUser: 7,
    pointEarnd: 70,
    date: new Date('2025-01-25')
  },
  {
    id: '8',
    name: 'Hannah Lee',
    email: '<EMAIL>',
    referralCode: 108,
    totalReferralUser: 1,
    pointEarnd: 10,
    date: new Date('2025-01-28')
  },
  {
    id: '9',
    name: 'Ian Somerhalder',
    email: '<EMAIL>',
    referralCode: 109,
    totalReferralUser: 9,
    pointEarnd: 90,
    date: new Date('2025-01-30')
  },
  {
    id: '10',
    name: 'Julia Roberts',
    email: '<EMAIL>',
    referralCode: 110,
    totalReferralUser: 10,
    pointEarnd: 100,
    date: new Date('2025-02-01')
  }
];
export const dummyFinanceData: FinanceProps[] = [
  {
    id: '1',
    orderId: 'ORD-1001',
    customerName: 'John Doe',
    profileUrl: 'https://randomuser.me/api/portraits/thumb/men/25.jpg',
    amount: '120.00',
    discount: '10.00',
    serviceFee: '5.00',
    transportFee: '8.00',
    vat: '11.00',
    totalFee: '134.00',
    status: 'Paid',
    date: '2025-08-01',
    remark: 'Paid via credit card'
  },
  {
    id: '2',
    orderId: 'ORD-1002',
    customerName: 'Jane Smith',
    profileUrl: 'https://randomuser.me/api/portraits/thumb/men/25.jpg',
    amount: '250.00',
    discount: '0.00',
    serviceFee: '7.00',
    transportFee: '12.00',
    vat: '26.90',
    totalFee: '295.90',
    status: 'Pending',
    date: '2025-08-01',
    remark: 'Awaiting bank transfer'
  },
  {
    id: '3',
    orderId: 'ORD-1003',
    customerName: 'Michael Johnson',
    profileUrl: 'https://randomuser.me/api/portraits/thumb/men/25.jpg',
    amount: '80.00',
    discount: '5.00',
    serviceFee: '3.00',
    transportFee: '6.00',
    vat: '7.40',
    totalFee: '91.40',
    status: 'Cancelled',
    date: '2025-08-01',
    remark: 'Customer cancelled before shipping'
  },
  {
    id: '4',
    orderId: 'ORD-1004',
    customerName: 'Emily Davis',
    profileUrl: 'https://randomuser.me/api/portraits/thumb/men/25.jpg',
    amount: '500.00',
    discount: '50.00',
    serviceFee: '10.00',
    transportFee: '15.00',
    vat: '45.50',
    totalFee: '520.50',
    status: 'Paid',
    date: '2025-08-01',
    remark: 'VIP customer'
  },
  {
    id: '5',
    orderId: 'ORD-1005',
    customerName: 'Chris Wilson',
    profileUrl: 'https://randomuser.me/api/portraits/thumb/men/25.jpg',
    amount: '60.00',
    discount: '0.00',
    serviceFee: '2.00',
    transportFee: '5.00',
    vat: '6.70',
    totalFee: '73.70',
    status: 'Pending',
    date: '2025-08-01',
    remark: '-'
  }
];

export const rolesData: RolesProps[] = [
  {
    id: 'role-1',
    role: 'SuperAdmin',
    status: 'Active',
    createdAt: new Date('2025-01-05T09:30:00Z'),
    createdBy: 'Sophia'
  },
  {
    id: 'role-2',
    role: 'Admin',
    status: 'Inactive',
    createdAt: new Date('2025-02-12T14:15:00Z'),
    createdBy: 'Liam'
  },
  {
    id: 'role-3',
    role: 'User',
    status: 'Pending',
    createdAt: new Date('2025-03-01T08:45:00Z'),
    createdBy: 'Mia'
  }
];
