export enum QUERY_KEY_ENUM {
  ORDERS = 'list-order',
  CALENDAR_ORDERS = 'list-calendar-order',
  CANCEL_ORDER = 'cancel-order',
  CATEGORIES = 'list-category',
  CATEGORIES_NAME = 'list-category-name',
  CATEGORIES_NAME_REGISTER = 'categoryName',

  CATEGORY_REARRANGE = 'category-rearrange',
  ORDER_DETAIL = 'order-detail',
  CLEANER_DETAIL = 'cleaner-detail',
  UPDATE_ORDER = 'update-order',
  EXPORT_EXCEL = 'export-excel',
  ANNOUNCEMENT = 'announcement',

  CLEANERS = 'cleaners',
  CREATE_CLEANERS = 'create-cleaners',
  UPLOAD_CLEANER_IMAGE = 'upload-cleaner-image',
  ADD_CLEANER_DETAIL = 'add-cleaner-detail',
  UPDATE_CLEANER_EXPERTISE = 'update-cleaner-expertise',

  SERVICE_ITEM_DETAIL = 'service-item-detail',
  SERVICE_ITEMS = 'service-items',
  CREATE_SERVICE_ITEMS = 'create-service-items',
  ADD_SERVICE_ITEM_DETAILS = 'add-service-item-details',

  CUSTOMERS = 'customers',
  EXISTED_CUSTOMER = 'existed-customer',
  REGISTER_CUSTOMERS = 'register-customers',
  UPDATE_CUSTOMER_SERVICE = 'update-customer-service',
  CUSTOMER_LAST_CONTACT_DATE = 'customer-last-contact-date',

  RESCHEDULE = 'reschedule',
  RESOUCE_REFERRAL = 'resource-referral',

  CREATE_COUPON = 'create-coupon',
  COUPONS = 'counpons',
  COUPON_LIST_SUMMARY = 'coupon-list-summary',
  COUPON_DETAIL = 'coupon-detail',
  ADD_USER_TO_COUPON = 'add-user-to-coupon',
  REMOVE_USER_FROM_COUPON = 'remove-user-from-coupon',
  GET_SELECTED_PRODUCT_DETAIL = 'get-selected-product-detail',

  OTP = 'otp',
  BLOCKED_SCHEDULE = 'blocked-schedule',
  CREATE_BLOCKED_SCHEDULE = 'create-blocked-schedule',
  BLOCKED_SCHEDULE_DETAIL = 'blocked-schedule-detail',
  CREATE_TOPUP = 'create-topup',
  PAYMENT_LINK = 'payment-link',
  ADD_PAYMENT_LINK = 'add-payment-link',
  CHECK_PAYMENT_STATUS = 'check-payment-status',
  PRODUCT_DETAIL_BY_CATEGORY_ID = 'product-detail-by-category-id',
  PRODUCT_OPTION_BY_PRODUCT_ID = 'product-option-by-product-id',

  DIRECT_SALE_PREVIEW = 'direct-sale-preview',
  DIRECT_SALE_USERS = 'direct-sale-users',
  DIRECT_SALE_USERS_ADDRESS = 'direct-sale-users-address',
  ADD_DIRECT_SALE_USERS_ADDRESS = 'add-direct-sale-users-address',
  GET_DATA_DIRECT_SALE = 'get-data-direct-sale',
  CREATE_DIRECT_ORDER = 'create-direct-order',
  PRODUCT_DIRECT_SALE = 'product-direct-sale',
  EDIT_NOTE = 'edit-note',
  ABA_PAYMENT_STATUS = 'aba-payment-status',
  LIST_DIRECT_SALE = 'list-direct-sale',
  CHECK_TOPUP_PAYMENT_STATUS = 'check-topup-payment-status',
  MARK_AS_PAID = 'mark-as-paid',
  UPDATE_ORDER_STATUS = 'update-order-status',
  DELETE_RECEIPT = 'delete-receipt',
  MARKETING_OVERVIEW = 'marketing-overview',
  CUSRTOMER_RATING_DETAILS = 'cusrtomer-rating-details',
  CUSTOMER_TYPE_DIRECT_SALE = 'customer-type-direct-sale',
  CREATE_DIRECT_SALE_CUSTOMER = 'create-direct-sale-customer',
  CUSTOMER_DIRECT_SALE_USERS = 'customer-direct-sale-users'
}
