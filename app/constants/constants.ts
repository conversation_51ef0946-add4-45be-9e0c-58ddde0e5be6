// button-utils.ts

import moment from 'moment';

/**
 * Determines if a button should be disabled.
 * @param condition - boolean condition to check
 * @returns boolean
 */
export const isButtonDisabled = (condition: boolean): boolean => {
  return condition;
};

export const CONSTANTS = {
  LIMIT_PER_PAGE: 15
};

export const TEMP_HIDE = true;

export const initialDateRange = {
  // previous 3 months
  from: moment('2025-07-01').toDate(),
  to: moment().toDate()
};
