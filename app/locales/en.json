{"header": {"financeOrders": "Orders", "bCombos": "bCombos", "directSales": "Direct Sales", "dashboard": "Dashboard", "order": "Order", "users": "Users", "newUser": "New User", "category": "Category", "newCategory": "New Category", "categoryAddon": "Category Add-On", "product": "Product", "newProduct": "New Product", "productOption": "Product Option", "newProductOption": "New Product Option", "newPromotion": "New Promotion", "customer": "Customer", "voucher": "Voucher", "newVoucher": "New Voucher", "editVoucher": "<PERSON>", "banner": "Banner", "newBanner": "New Banner", "serviceBundle": "Service Bundle", "newServiceBundle": "New Service Bundle", "auditLog": "<PERSON>t Log", "topUp": "Top Up", "referralProgram": "Referral Program", "notifications": "Notifications", "setup": "Setup", "newCategoryAddon": "New Category Add-On", "promotions": "Promotions", "pushNotification": "Push Notification", "newPushNotification": "New Push Notification", "finance": "Finance", "roles": "Roles", "newRoles": "New Role", "items": "Items", "newItem": "New Item", "cleaners": "Cleaners", "newCleaner": "New Cleaner", "editCleaner": "Edit Cleaner", "editItem": "<PERSON>em", "coupon": "Coupon", "newCoupon": "New Coupon", "editCoupon": "Edit Coupon"}, "appSidebar": {"dashboard": "Dashboard", "order": "Order", "services": "Services", "category": "Category", "categoryAddon": "Category Add-On", "product": "Product", "productOption": "Product Option", "user": "User", "customer": "Customer", "voucher": "Voucher", "banner": "Banner", "serviceBundle": "Service Bundle", "auditLog": "<PERSON>t Log", "topUp": "Top Up", "referralProgram": "Referral Program", "notifications": "Notifications", "setup": "Setup", "all": "All", "read": "Read", "noNotifications": "No notifications.", "noReadNotifications": "No read notifications.", "markAllAsRead": "Mark all as read", "marketing": "Marketing", "promotions": "Promotions", "pushNotification": "Push Notification", "finance": "Finance", "users": "Users", "roles": "Roles", "orders": "Orders", "bCombos": "bCombos", "directSales": "Direct Sales", "items": "Items", "cleaners": "Cleaners", "coupon": "Coupon"}, "categoryPage": {"addProduct": "Add Product", "addCategoryAddOn": "Add Category Add-On", "product": "Product", "categoryAddOn": "Category Add-On", "taskInformation": "Task Information", "details": "Details", "productVariant": "Product Variant", "addAnotherVariant": "Add <PERSON> Variant", "clearAll": "Clear All", "addServiceBundle": "Add Service Bundle"}, "productPage": {"newProduct": "New Product", "details": "Details", "taskInformation": "Task Information", "productOption": "Product Option", "addProductOption": "Add Product Option"}, "serviceBundlePage": {"newServiceBundle": "New Service Bundle", "details": "Details", "description": "Bundle Description", "service": "Services"}, "promotionPage": {"newPromotion": "New Promotion", "details": "Details", "taskInformation": "Task Information", "promotion": "Promotion", "addPromotion": "New Promotion"}, "noResults": "No Results.", "save": "Save", "addContent": "Add Content", "cancel": "Cancel", "addAnother": "Add Another", "name": "Name", "price": "Price", "status": "Status", "category": "Category", "optional": "(optional)", "title": "Title", "hours": "Hours", "bedroom": "Bedroom", "floor": "Floor", "cleaners": "Cleaners", "pestControl": "Pest Control", "technician": "Technician", "allStatus": "All Status", "paymentStatus": "Payment Status", "send": "Send", "chooseDate": "<PERSON>ose <PERSON>"}